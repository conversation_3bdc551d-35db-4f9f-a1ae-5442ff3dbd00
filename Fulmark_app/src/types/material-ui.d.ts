import '@mui/material/styles';
import '@mui/material/Button';
import '@mui/material/Fab';
import '@mui/material/Paper';

declare module '@mui/material/Button' {
  interface ButtonPropsVariantOverrides {
    gradient: true;
    glass: true;
    cosmic: true;
    neon: true;
  }
}

declare module '@mui/material/Fab' {
  interface FabPropsVariantOverrides {
    gradient: true;
    glass: true;
    cosmic: true;
  }
}

declare module '@mui/material/Paper' {
  interface PaperPropsVariantOverrides {
    gradient: true;
    glass: true;
    elevated: true;
  }
}

declare module '@mui/material/styles' {
  interface Palette {
    hvac: {
      cooling: string;
      heating: string;
      ventilation: string;
      maintenance: string;
      urgent: string;
      scheduled: string;
      completed: string;
      inactive: string;
    };
  }
  interface PaletteOptions {
    hvac?: {
      cooling?: string;
      heating?: string;
      ventilation?: string;
      maintenance?: string;
      urgent?: string;
      scheduled?: string;
      completed?: string;
      inactive?: string;
    };
  }
}