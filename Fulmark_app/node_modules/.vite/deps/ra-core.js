import {
  AUTH_CHECK,
  AUTH_ERROR,
  AUTH_GET_PERMISSIONS,
  AUTH_LOGIN,
  AUTH_LOGOUT,
  AddNotificationContext,
  AddUndoableMutationContext,
  AdminRouter,
  AuthContext,
  Authenticated,
  BasenameContextProvider,
  CREATE,
  CanAccess,
  ChoicesContext,
  ChoicesContextProvider,
  CoreAdmin,
  CoreAdminContext,
  CoreAdminRoutes,
  CoreAdminUI,
  CreateBase,
  CreateContext,
  CreateContextProvider,
  CreateController,
  CustomRoutes,
  DEFAULT_LOCALE,
  DELETE,
  DELETE_MANY,
  DataProviderContext_default,
  DefaultTitleContext,
  DefaultTitleContextProvider,
  EditBase,
  EditContext,
  EditContextProvider,
  EditController,
  ExporterContext,
  FieldTitle_default,
  FilterLiveForm,
  Form,
  FormDataConsumer,
  FormDataConsumerView,
  FormGroupContext,
  FormGroupContextProvider,
  FormGroupsProvider,
  GET_LIST,
  GET_MANY,
  GET_MANY_REFERENCE,
  GET_ONE,
  HIDE_FILTER,
  HasDashboardContext,
  HasDashboardContextProvider,
  HttpError_default,
  I18N_CHANGE_LOCALE,
  I18N_TRANSLATE,
  I18nContext,
  I18nContextProvider,
  InferenceTypes,
  InferredElement_default,
  InfiniteListBase,
  InfinitePaginationContext,
  ListBase,
  ListContext,
  ListContextProvider,
  ListController,
  ListFilterContext,
  ListPaginationContext,
  ListSortContext,
  LogoutOnMount,
  NavigateToFirstResource,
  NotificationContext,
  NotificationContextProvider,
  OptionalRecordContextProvider,
  OptionalResourceContextProvider,
  PreferenceKeyContext,
  PreferenceKeyContextProvider,
  PreferencesEditorContext,
  PreferencesEditorContextProvider,
  PreviousLocationStorageKey,
  Ready_default,
  RecordContext,
  RecordContextProvider,
  RecordRepresentation,
  ReferenceFieldBase,
  ReferenceFieldContext,
  ReferenceFieldContextProvider,
  ReferenceInputBase,
  Resource,
  ResourceContext,
  ResourceContextProvider,
  ResourceDefinitionContext,
  ResourceDefinitionContextProvider,
  RestoreScrollPosition,
  SET_FILTER,
  SET_PAGE,
  SET_PER_PAGE,
  SET_SORT,
  SHOW_FILTER,
  SORT_ASC,
  SORT_DESC,
  SaveContext,
  SaveContextProvider,
  ShowBase,
  ShowContext,
  ShowContextProvider,
  ShowController,
  SourceContext,
  SourceContextProvider,
  StoreContext,
  StoreContextProvider,
  StoreSetter,
  TakeUndoableMutationContext,
  TestMemoryRouter,
  TestTranslationProvider,
  TranslatableContext,
  TranslatableContextProvider,
  UPDATE,
  UPDATE_MANY,
  UndoableMutationsContextProvider,
  ValidationError,
  WarnWhenUnsavedChanges,
  WithListContext,
  WithPermissions_default,
  WithRecord,
  addRefreshAuthToAuthProvider,
  addRefreshAuthToDataProvider,
  applyCallbacks,
  asyncDebounce,
  choices,
  combine2Validators,
  combineDataProviders,
  composeSyncValidators,
  composeValidators,
  convertLegacyAuthProvider_default,
  convertLegacyDataProvider_default,
  defaultDataProvider,
  defaultExporter,
  downloadCSV,
  email,
  escapePath_default,
  fetchActionsWithArrayOfIdentifiedRecordsResponse,
  fetchActionsWithArrayOfRecordsResponse,
  fetchActionsWithRecordResponse,
  fetchActionsWithTotalResponse,
  fetchRelatedRecords,
  fetch_exports,
  getElementsFromRecords_default,
  getFieldLabelTranslationArgs,
  getFilterFormValues,
  getFormGroupState,
  getListControllerProps,
  getMutationMode,
  getNumberOrDefault,
  getQuery,
  getRecordForLocale,
  getRecordFromLocation,
  getResourceFieldLabelKey,
  getSelectedReferencesStatus,
  getSimpleValidationResolver,
  getStatusForArrayInput,
  getStatusForInput,
  getStorage,
  getSuggestionsFactory,
  getValuesFromRecords_default,
  hasCustomParams,
  inferTypeFromValues,
  injectedProps,
  isEmpty,
  isRequired,
  localStorageStore,
  maxLength,
  maxValue,
  memoryStore,
  mergeRefs,
  mergeTranslations,
  minLength,
  minValue,
  number,
  parseQueryFromLocation,
  queryReducer,
  reactAdminFetchActions,
  regex,
  removeDoubleSlashes,
  removeEmpty_default,
  removeKey_default,
  required,
  resolveBrowserLocale,
  sanitizeFetchType,
  sanitizeListRestProps,
  setSubmissionErrors,
  shallowEqual,
  substituteTokens,
  testDataProvider,
  testI18nProvider,
  undoableEventEmitter_default,
  useAddNotificationContext,
  useAddUndoableMutation,
  useApplyInputDefaultValues,
  useAugmentedForm,
  useAuthProvider_default,
  useAuthState_default,
  useAuthenticated,
  useBasename,
  useCanAccess,
  useCanAccessCallback,
  useCanAccessResources,
  useCheckAuth,
  useCheckForApplicationUpdate,
  useCheckMinimumRequiredProps,
  useChoices,
  useChoicesContext,
  useCreate,
  useCreateContext,
  useCreateController,
  useCreatePath,
  useDataProvider,
  useDebouncedEvent,
  useDeepCompareEffect,
  useDefaultTitle,
  useDelete,
  useDeleteMany,
  useDeleteWithConfirmController_default,
  useDeleteWithUndoController_default,
  useEditContext,
  useEditController,
  useEvent,
  useExpandAll,
  useExpanded,
  useFieldValue,
  useFilterState_default,
  useFirstResourceWithListAccess,
  useFormGroup,
  useFormGroupContext,
  useFormGroups,
  useGetIdentity,
  useGetList,
  useGetMany,
  useGetManyAggregate,
  useGetManyReference,
  useGetOne,
  useGetPathForRecord,
  useGetPathForRecordCallback,
  useGetPermissions_default,
  useGetRecordId,
  useGetRecordRepresentation,
  useGetResourceLabel,
  useGetValidationErrorMessage,
  useHandleAuthCallback,
  useHasDashboard,
  useI18nProvider,
  useInfiniteGetList,
  useInfiniteListController,
  useInfinitePaginationContext,
  useInput,
  useIsAuthPending,
  useIsDataLoaded,
  useIsMounted,
  useList,
  useListContext,
  useListContextWithProps,
  useListController,
  useListFilterContext,
  useListPaginationContext,
  useListParams,
  useListSortContext,
  useLoading,
  useLocale,
  useLocaleState,
  useLocales,
  useLogin_default,
  useLogoutIfAccessDenied_default,
  useLogout_default,
  useMutationMiddlewares,
  useNotificationContext,
  useNotify,
  useNotifyIsFormInvalid,
  useOptionalSourceContext,
  usePaginationState_default,
  usePermissions_default,
  usePickFilterContext,
  usePickPaginationContext,
  usePickSaveContext,
  usePickSortContext,
  usePreference,
  usePreferenceInput,
  usePreferenceKey,
  usePreferencesEditor,
  usePrevNextController,
  usePrevious,
  useRecordContext,
  useRecordFromLocation,
  useRecordSelection,
  useRedirect,
  useReference,
  useReferenceArrayFieldController,
  useReferenceArrayInputController,
  useReferenceFieldContext,
  useReferenceFieldController,
  useReferenceInputController,
  useReferenceManyFieldController,
  useReferenceOneFieldController,
  useRefresh,
  useRegisterMutationMiddleware,
  useRemoveFromStore,
  useRemoveItemsFromStore,
  useRequireAccess,
  useResetErrorBoundaryOnLocationChange,
  useResetStore,
  useResourceContext,
  useResourceDefinition,
  useResourceDefinitionContext,
  useResourceDefinitions,
  useRestoreScrollPosition,
  useSafeSetState,
  useSaveContext,
  useScrollToTop,
  useSelectAll,
  useSetInspectorTitle,
  useSetLocale,
  useShowContext,
  useShowController,
  useSortState_default,
  useSourceContext,
  useSplatPathBase,
  useStore,
  useStoreContext,
  useSuggestions,
  useTakeUndoableMutation,
  useTimeout,
  useTrackScrollPosition,
  useTranslatable,
  useTranslatableContext,
  useTranslate,
  useTranslateLabel,
  useUnique,
  useUnselect,
  useUnselectAll,
  useUpdate,
  useUpdateMany,
  useWarnWhenUnsavedChanges,
  useWhyDidYouUpdate,
  useWrappedSource,
  warning_default,
  withLifecycleCallbacks
} from "./chunk-NXOTDOAB.js";
import "./chunk-JHPPEXU2.js";
import "./chunk-FIB7OQ2W.js";
import "./chunk-AROJ2QES.js";
import "./chunk-OYP4QM2F.js";
import "./chunk-HU6PDPOE.js";
import "./chunk-K5SMX77J.js";
import "./chunk-CZVV7UDY.js";
import "./chunk-QAMXTZP5.js";
import "./chunk-F34GCA6J.js";
import "./chunk-TUVFVZDQ.js";
import "./chunk-CRNJR6QK.js";
import "./chunk-ZMLY2J2T.js";
import "./chunk-4B2QHNJT.js";
export {
  AUTH_CHECK,
  AUTH_ERROR,
  AUTH_GET_PERMISSIONS,
  AUTH_LOGIN,
  AUTH_LOGOUT,
  AddNotificationContext,
  AddUndoableMutationContext,
  AdminRouter,
  AuthContext,
  Authenticated,
  BasenameContextProvider,
  CREATE,
  CanAccess,
  ChoicesContext,
  ChoicesContextProvider,
  CoreAdmin,
  CoreAdminContext,
  CoreAdminRoutes,
  CoreAdminUI,
  CreateBase,
  CreateContext,
  CreateContextProvider,
  CreateController,
  CustomRoutes,
  DEFAULT_LOCALE,
  DELETE,
  DELETE_MANY,
  DataProviderContext_default as DataProviderContext,
  DefaultTitleContext,
  DefaultTitleContextProvider,
  EditBase,
  EditContext,
  EditContextProvider,
  EditController,
  ExporterContext,
  FieldTitle_default as FieldTitle,
  FilterLiveForm,
  Form,
  FormDataConsumer,
  FormDataConsumerView,
  FormGroupContext,
  FormGroupContextProvider,
  FormGroupsProvider,
  GET_LIST,
  GET_MANY,
  GET_MANY_REFERENCE,
  GET_ONE,
  HIDE_FILTER,
  HasDashboardContext,
  HasDashboardContextProvider,
  HttpError_default as HttpError,
  I18N_CHANGE_LOCALE,
  I18N_TRANSLATE,
  I18nContext,
  I18nContextProvider,
  InferenceTypes,
  InferredElement_default as InferredElement,
  InfiniteListBase,
  InfinitePaginationContext,
  ListBase,
  ListContext,
  ListContextProvider,
  ListController,
  ListFilterContext,
  ListPaginationContext,
  ListSortContext,
  LogoutOnMount,
  NavigateToFirstResource,
  NotificationContext,
  NotificationContextProvider,
  OptionalRecordContextProvider,
  OptionalResourceContextProvider,
  PreferenceKeyContext,
  PreferenceKeyContextProvider,
  PreferencesEditorContext,
  PreferencesEditorContextProvider,
  PreviousLocationStorageKey,
  Ready_default as Ready,
  RecordContext,
  RecordContextProvider,
  RecordRepresentation,
  ReferenceFieldBase,
  ReferenceFieldContext,
  ReferenceFieldContextProvider,
  ReferenceInputBase,
  Resource,
  ResourceContext,
  ResourceContextProvider,
  ResourceDefinitionContext,
  ResourceDefinitionContextProvider,
  RestoreScrollPosition,
  SET_FILTER,
  SET_PAGE,
  SET_PER_PAGE,
  SET_SORT,
  SHOW_FILTER,
  SORT_ASC,
  SORT_DESC,
  SaveContext,
  SaveContextProvider,
  ShowBase,
  ShowContext,
  ShowContextProvider,
  ShowController,
  SourceContext,
  SourceContextProvider,
  StoreContext,
  StoreContextProvider,
  StoreSetter,
  TakeUndoableMutationContext,
  TestMemoryRouter,
  TestTranslationProvider,
  TranslatableContext,
  TranslatableContextProvider,
  UPDATE,
  UPDATE_MANY,
  UndoableMutationsContextProvider,
  ValidationError,
  WarnWhenUnsavedChanges,
  WithListContext,
  WithPermissions_default as WithPermissions,
  WithRecord,
  addRefreshAuthToAuthProvider,
  addRefreshAuthToDataProvider,
  applyCallbacks,
  asyncDebounce,
  choices,
  combine2Validators,
  combineDataProviders,
  composeSyncValidators,
  composeValidators,
  convertLegacyAuthProvider_default as convertLegacyAuthProvider,
  convertLegacyDataProvider_default as convertLegacyDataProvider,
  defaultDataProvider,
  defaultExporter,
  downloadCSV,
  email,
  escapePath_default as escapePath,
  fetchActionsWithArrayOfIdentifiedRecordsResponse,
  fetchActionsWithArrayOfRecordsResponse,
  fetchActionsWithRecordResponse,
  fetchActionsWithTotalResponse,
  fetchRelatedRecords,
  fetch_exports as fetchUtils,
  getElementsFromRecords_default as getElementsFromRecords,
  getFieldLabelTranslationArgs,
  getFilterFormValues,
  getFormGroupState,
  getListControllerProps,
  getMutationMode,
  getNumberOrDefault,
  getQuery,
  getRecordForLocale,
  getRecordFromLocation,
  getResourceFieldLabelKey,
  getSelectedReferencesStatus,
  getSimpleValidationResolver,
  getStatusForArrayInput,
  getStatusForInput,
  getStorage,
  getSuggestionsFactory,
  getValuesFromRecords_default as getValuesFromRecords,
  hasCustomParams,
  inferTypeFromValues,
  injectedProps,
  isEmpty,
  isRequired,
  localStorageStore,
  maxLength,
  maxValue,
  memoryStore,
  mergeRefs,
  mergeTranslations,
  minLength,
  minValue,
  number,
  parseQueryFromLocation,
  queryReducer,
  reactAdminFetchActions,
  regex,
  removeDoubleSlashes,
  removeEmpty_default as removeEmpty,
  removeKey_default as removeKey,
  required,
  resolveBrowserLocale,
  sanitizeFetchType,
  sanitizeListRestProps,
  setSubmissionErrors,
  shallowEqual,
  substituteTokens,
  testDataProvider,
  testI18nProvider,
  undoableEventEmitter_default as undoableEventEmitter,
  useAddNotificationContext,
  useAddUndoableMutation,
  useApplyInputDefaultValues,
  useAugmentedForm,
  useAuthProvider_default as useAuthProvider,
  useAuthState_default as useAuthState,
  useAuthenticated,
  useBasename,
  useCanAccess,
  useCanAccessCallback,
  useCanAccessResources,
  useCheckAuth,
  useCheckForApplicationUpdate,
  useCheckMinimumRequiredProps,
  useChoices,
  useChoicesContext,
  useCreate,
  useCreateContext,
  useCreateController,
  useCreatePath,
  useDataProvider,
  useDebouncedEvent,
  useDeepCompareEffect,
  useDefaultTitle,
  useDelete,
  useDeleteMany,
  useDeleteWithConfirmController_default as useDeleteWithConfirmController,
  useDeleteWithUndoController_default as useDeleteWithUndoController,
  useEditContext,
  useEditController,
  useEvent,
  useExpandAll,
  useExpanded,
  useFieldValue,
  useFilterState_default as useFilterState,
  useFirstResourceWithListAccess,
  useFormGroup,
  useFormGroupContext,
  useFormGroups,
  useGetIdentity,
  useGetList,
  useGetMany,
  useGetManyAggregate,
  useGetManyReference,
  useGetOne,
  useGetPathForRecord,
  useGetPathForRecordCallback,
  useGetPermissions_default as useGetPermissions,
  useGetRecordId,
  useGetRecordRepresentation,
  useGetResourceLabel,
  useGetValidationErrorMessage,
  useHandleAuthCallback,
  useHasDashboard,
  useI18nProvider,
  useInfiniteGetList,
  useInfiniteListController,
  useInfinitePaginationContext,
  useInput,
  useIsAuthPending,
  useIsDataLoaded,
  useIsMounted,
  useList,
  useListContext,
  useListContextWithProps,
  useListController,
  useListFilterContext,
  useListPaginationContext,
  useListParams,
  useListSortContext,
  useLoading,
  useLocale,
  useLocaleState,
  useLocales,
  useLogin_default as useLogin,
  useLogout_default as useLogout,
  useLogoutIfAccessDenied_default as useLogoutIfAccessDenied,
  useMutationMiddlewares,
  useNotificationContext,
  useNotify,
  useNotifyIsFormInvalid,
  useOptionalSourceContext,
  usePaginationState_default as usePaginationState,
  usePermissions_default as usePermissions,
  usePickFilterContext,
  usePickPaginationContext,
  usePickSaveContext,
  usePickSortContext,
  usePreference,
  usePreferenceInput,
  usePreferenceKey,
  usePreferencesEditor,
  usePrevNextController,
  usePrevious,
  useRecordContext,
  useRecordFromLocation,
  useRecordSelection,
  useRedirect,
  useReference,
  useReferenceArrayFieldController,
  useReferenceArrayInputController,
  useReferenceFieldContext,
  useReferenceFieldController,
  useReferenceInputController,
  useReferenceManyFieldController,
  useReferenceOneFieldController,
  useRefresh,
  useRegisterMutationMiddleware,
  useRemoveFromStore,
  useRemoveItemsFromStore,
  useRequireAccess,
  useResetErrorBoundaryOnLocationChange,
  useResetStore,
  useResourceContext,
  useResourceDefinition,
  useResourceDefinitionContext,
  useResourceDefinitions,
  useRestoreScrollPosition,
  useSafeSetState,
  useSaveContext,
  useScrollToTop,
  useSelectAll,
  useSetInspectorTitle,
  useSetLocale,
  useShowContext,
  useShowController,
  useSortState_default as useSortState,
  useSourceContext,
  useSplatPathBase,
  useStore,
  useStoreContext,
  useSuggestions,
  useTakeUndoableMutation,
  useTimeout,
  useTrackScrollPosition,
  useTranslatable,
  useTranslatableContext,
  useTranslate,
  useTranslateLabel,
  useUnique,
  useUnselect,
  useUnselectAll,
  useUpdate,
  useUpdateMany,
  useWarnWhenUnsavedChanges,
  useWhyDidYouUpdate,
  useWrappedSource,
  warning_default as warning,
  withLifecycleCallbacks
};
