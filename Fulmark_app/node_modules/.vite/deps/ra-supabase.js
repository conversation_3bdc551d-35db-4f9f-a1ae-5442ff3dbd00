import {
  Admin<PERSON>ontext,
  <PERSON>min<PERSON>,
  <PERSON><PERSON><PERSON>iew,
  EditView,
  Link,
  ListView,
  Loading,
  Notification,
  PasswordInput,
  SaveButton,
  SearchInput,
  ShowView,
  TextInput,
  defaultTheme,
  editFieldTypes,
  listFieldTypes,
  require_Lock,
  showFieldTypes
} from "./chunk-ZDEHGQQN.js";
import "./chunk-MDVBKQXJ.js";
import {
  CreateBase,
  CustomRoutes,
  EditBase,
  Form,
  InferredElement_default,
  ListBase,
  Resource,
  ShowBase,
  fetch_exports,
  mergeTranslations,
  require_inflection,
  required,
  useAuthProvider_default,
  useCheckAuth,
  useDataProvider,
  useLogin_default,
  useNotify,
  useRedirect,
  useResourceContext,
  useTranslate
} from "./chunk-NXOTDOAB.js";
import "./chunk-JHPPEXU2.js";
import "./chunk-FIB7OQ2W.js";
import {
  BrowserRouter,
  Route,
  useNavigate
} from "./chunk-AROJ2QES.js";
import {
  require_call_bound,
  require_get_intrinsic,
  require_type,
  src_default
} from "./chunk-ZD2CX3EA.js";
import {
  src_default as src_default2
} from "./chunk-PGGIMZ4Q.js";
import "./chunk-BHXS6QRH.js";
import "./chunk-OYP4QM2F.js";
import {
  createClient
} from "./chunk-LU7GVDZY.js";
import "./chunk-HWMC2YUY.js";
import {
  useMutation,
  useQuery
} from "./chunk-HU6PDPOE.js";
import "./chunk-K5SMX77J.js";
import "./chunk-CZVV7UDY.js";
import {
  require_isEqual
} from "./chunk-QAMXTZP5.js";
import "./chunk-D3EWPWVD.js";
import {
  Avatar_default,
  Button_default,
  CardActions_default,
  Divider_default,
  Stack_default
} from "./chunk-DJCUNCJ5.js";
import "./chunk-ZCLI3QBQ.js";
import {
  Card_default
} from "./chunk-LUPKCG7P.js";
import "./chunk-KECX7V7S.js";
import "./chunk-5TPTZXJA.js";
import "./chunk-2TD3YL3A.js";
import "./chunk-6MHFPJK6.js";
import "./chunk-OBXFZZOX.js";
import "./chunk-VUVLQFN2.js";
import "./chunk-AB4BJMS4.js";
import {
  Typography_default
} from "./chunk-XRVPZ2IY.js";
import {
  ThemeProvider
} from "./chunk-O2MGGYXR.js";
import "./chunk-NO2WNSFZ.js";
import "./chunk-IPJXDFYY.js";
import "./chunk-NVWFZNBI.js";
import "./chunk-OBKYI77C.js";
import "./chunk-K4VEMI7V.js";
import "./chunk-YEFOXO7V.js";
import "./chunk-F6ZHV63J.js";
import "./chunk-HMRDMR7W.js";
import "./chunk-DQQV4UC5.js";
import "./chunk-IG5BTQOZ.js";
import "./chunk-DJ5VV5B5.js";
import "./chunk-F34GCA6J.js";
import "./chunk-MDAAAS2S.js";
import "./chunk-I5R4IOND.js";
import {
  SvgIcon_default
} from "./chunk-UADODFX5.js";
import "./chunk-Y47FPZQ5.js";
import "./chunk-K2ESJMZZ.js";
import "./chunk-WIEETYZY.js";
import "./chunk-C2PABMSY.js";
import "./chunk-IFE6TQG2.js";
import {
  createTheme_default2 as createTheme_default,
  styled_default
} from "./chunk-XYYFCDBB.js";
import "./chunk-ZQ3FPKKL.js";
import "./chunk-TUVFVZDQ.js";
import "./chunk-NHY3NUFE.js";
import {
  require_jsx_runtime
} from "./chunk-CRNJR6QK.js";
import "./chunk-QZVMM6GT.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __commonJS,
  __toESM
} from "./chunk-4B2QHNJT.js";

// (disabled):node_modules/object-inspect/util.inspect
var require_util = __commonJS({
  "(disabled):node_modules/object-inspect/util.inspect"() {
  }
});

// node_modules/object-inspect/index.js
var require_object_inspect = __commonJS({
  "node_modules/object-inspect/index.js"(exports, module) {
    var hasMap = typeof Map === "function" && Map.prototype;
    var mapSizeDescriptor = Object.getOwnPropertyDescriptor && hasMap ? Object.getOwnPropertyDescriptor(Map.prototype, "size") : null;
    var mapSize = hasMap && mapSizeDescriptor && typeof mapSizeDescriptor.get === "function" ? mapSizeDescriptor.get : null;
    var mapForEach = hasMap && Map.prototype.forEach;
    var hasSet = typeof Set === "function" && Set.prototype;
    var setSizeDescriptor = Object.getOwnPropertyDescriptor && hasSet ? Object.getOwnPropertyDescriptor(Set.prototype, "size") : null;
    var setSize = hasSet && setSizeDescriptor && typeof setSizeDescriptor.get === "function" ? setSizeDescriptor.get : null;
    var setForEach = hasSet && Set.prototype.forEach;
    var hasWeakMap = typeof WeakMap === "function" && WeakMap.prototype;
    var weakMapHas = hasWeakMap ? WeakMap.prototype.has : null;
    var hasWeakSet = typeof WeakSet === "function" && WeakSet.prototype;
    var weakSetHas = hasWeakSet ? WeakSet.prototype.has : null;
    var hasWeakRef = typeof WeakRef === "function" && WeakRef.prototype;
    var weakRefDeref = hasWeakRef ? WeakRef.prototype.deref : null;
    var booleanValueOf = Boolean.prototype.valueOf;
    var objectToString = Object.prototype.toString;
    var functionToString = Function.prototype.toString;
    var $match = String.prototype.match;
    var $slice = String.prototype.slice;
    var $replace = String.prototype.replace;
    var $toUpperCase = String.prototype.toUpperCase;
    var $toLowerCase = String.prototype.toLowerCase;
    var $test = RegExp.prototype.test;
    var $concat = Array.prototype.concat;
    var $join = Array.prototype.join;
    var $arrSlice = Array.prototype.slice;
    var $floor = Math.floor;
    var bigIntValueOf = typeof BigInt === "function" ? BigInt.prototype.valueOf : null;
    var gOPS = Object.getOwnPropertySymbols;
    var symToString = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? Symbol.prototype.toString : null;
    var hasShammedSymbols = typeof Symbol === "function" && typeof Symbol.iterator === "object";
    var toStringTag = typeof Symbol === "function" && Symbol.toStringTag && (typeof Symbol.toStringTag === hasShammedSymbols ? "object" : "symbol") ? Symbol.toStringTag : null;
    var isEnumerable = Object.prototype.propertyIsEnumerable;
    var gPO = (typeof Reflect === "function" ? Reflect.getPrototypeOf : Object.getPrototypeOf) || ([].__proto__ === Array.prototype ? function(O) {
      return O.__proto__;
    } : null);
    function addNumericSeparator(num, str) {
      if (num === Infinity || num === -Infinity || num !== num || num && num > -1e3 && num < 1e3 || $test.call(/e/, str)) {
        return str;
      }
      var sepRegex = /[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;
      if (typeof num === "number") {
        var int = num < 0 ? -$floor(-num) : $floor(num);
        if (int !== num) {
          var intStr = String(int);
          var dec = $slice.call(str, intStr.length + 1);
          return $replace.call(intStr, sepRegex, "$&_") + "." + $replace.call($replace.call(dec, /([0-9]{3})/g, "$&_"), /_$/, "");
        }
      }
      return $replace.call(str, sepRegex, "$&_");
    }
    var utilInspect = require_util();
    var inspectCustom = utilInspect.custom;
    var inspectSymbol = isSymbol(inspectCustom) ? inspectCustom : null;
    var quotes = {
      __proto__: null,
      "double": '"',
      single: "'"
    };
    var quoteREs = {
      __proto__: null,
      "double": /(["\\])/g,
      single: /(['\\])/g
    };
    module.exports = function inspect_(obj, options, depth, seen) {
      var opts = options || {};
      if (has(opts, "quoteStyle") && !has(quotes, opts.quoteStyle)) {
        throw new TypeError('option "quoteStyle" must be "single" or "double"');
      }
      if (has(opts, "maxStringLength") && (typeof opts.maxStringLength === "number" ? opts.maxStringLength < 0 && opts.maxStringLength !== Infinity : opts.maxStringLength !== null)) {
        throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');
      }
      var customInspect = has(opts, "customInspect") ? opts.customInspect : true;
      if (typeof customInspect !== "boolean" && customInspect !== "symbol") {
        throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");
      }
      if (has(opts, "indent") && opts.indent !== null && opts.indent !== "	" && !(parseInt(opts.indent, 10) === opts.indent && opts.indent > 0)) {
        throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');
      }
      if (has(opts, "numericSeparator") && typeof opts.numericSeparator !== "boolean") {
        throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');
      }
      var numericSeparator = opts.numericSeparator;
      if (typeof obj === "undefined") {
        return "undefined";
      }
      if (obj === null) {
        return "null";
      }
      if (typeof obj === "boolean") {
        return obj ? "true" : "false";
      }
      if (typeof obj === "string") {
        return inspectString(obj, opts);
      }
      if (typeof obj === "number") {
        if (obj === 0) {
          return Infinity / obj > 0 ? "0" : "-0";
        }
        var str = String(obj);
        return numericSeparator ? addNumericSeparator(obj, str) : str;
      }
      if (typeof obj === "bigint") {
        var bigIntStr = String(obj) + "n";
        return numericSeparator ? addNumericSeparator(obj, bigIntStr) : bigIntStr;
      }
      var maxDepth = typeof opts.depth === "undefined" ? 5 : opts.depth;
      if (typeof depth === "undefined") {
        depth = 0;
      }
      if (depth >= maxDepth && maxDepth > 0 && typeof obj === "object") {
        return isArray(obj) ? "[Array]" : "[Object]";
      }
      var indent = getIndent(opts, depth);
      if (typeof seen === "undefined") {
        seen = [];
      } else if (indexOf(seen, obj) >= 0) {
        return "[Circular]";
      }
      function inspect(value, from, noIndent) {
        if (from) {
          seen = $arrSlice.call(seen);
          seen.push(from);
        }
        if (noIndent) {
          var newOpts = {
            depth: opts.depth
          };
          if (has(opts, "quoteStyle")) {
            newOpts.quoteStyle = opts.quoteStyle;
          }
          return inspect_(value, newOpts, depth + 1, seen);
        }
        return inspect_(value, opts, depth + 1, seen);
      }
      if (typeof obj === "function" && !isRegExp(obj)) {
        var name = nameOf(obj);
        var keys = arrObjKeys(obj, inspect);
        return "[Function" + (name ? ": " + name : " (anonymous)") + "]" + (keys.length > 0 ? " { " + $join.call(keys, ", ") + " }" : "");
      }
      if (isSymbol(obj)) {
        var symString = hasShammedSymbols ? $replace.call(String(obj), /^(Symbol\(.*\))_[^)]*$/, "$1") : symToString.call(obj);
        return typeof obj === "object" && !hasShammedSymbols ? markBoxed(symString) : symString;
      }
      if (isElement(obj)) {
        var s = "<" + $toLowerCase.call(String(obj.nodeName));
        var attrs = obj.attributes || [];
        for (var i = 0; i < attrs.length; i++) {
          s += " " + attrs[i].name + "=" + wrapQuotes(quote(attrs[i].value), "double", opts);
        }
        s += ">";
        if (obj.childNodes && obj.childNodes.length) {
          s += "...";
        }
        s += "</" + $toLowerCase.call(String(obj.nodeName)) + ">";
        return s;
      }
      if (isArray(obj)) {
        if (obj.length === 0) {
          return "[]";
        }
        var xs = arrObjKeys(obj, inspect);
        if (indent && !singleLineValues(xs)) {
          return "[" + indentedJoin(xs, indent) + "]";
        }
        return "[ " + $join.call(xs, ", ") + " ]";
      }
      if (isError(obj)) {
        var parts = arrObjKeys(obj, inspect);
        if (!("cause" in Error.prototype) && "cause" in obj && !isEnumerable.call(obj, "cause")) {
          return "{ [" + String(obj) + "] " + $join.call($concat.call("[cause]: " + inspect(obj.cause), parts), ", ") + " }";
        }
        if (parts.length === 0) {
          return "[" + String(obj) + "]";
        }
        return "{ [" + String(obj) + "] " + $join.call(parts, ", ") + " }";
      }
      if (typeof obj === "object" && customInspect) {
        if (inspectSymbol && typeof obj[inspectSymbol] === "function" && utilInspect) {
          return utilInspect(obj, { depth: maxDepth - depth });
        } else if (customInspect !== "symbol" && typeof obj.inspect === "function") {
          return obj.inspect();
        }
      }
      if (isMap(obj)) {
        var mapParts = [];
        if (mapForEach) {
          mapForEach.call(obj, function(value, key) {
            mapParts.push(inspect(key, obj, true) + " => " + inspect(value, obj));
          });
        }
        return collectionOf("Map", mapSize.call(obj), mapParts, indent);
      }
      if (isSet(obj)) {
        var setParts = [];
        if (setForEach) {
          setForEach.call(obj, function(value) {
            setParts.push(inspect(value, obj));
          });
        }
        return collectionOf("Set", setSize.call(obj), setParts, indent);
      }
      if (isWeakMap(obj)) {
        return weakCollectionOf("WeakMap");
      }
      if (isWeakSet(obj)) {
        return weakCollectionOf("WeakSet");
      }
      if (isWeakRef(obj)) {
        return weakCollectionOf("WeakRef");
      }
      if (isNumber(obj)) {
        return markBoxed(inspect(Number(obj)));
      }
      if (isBigInt(obj)) {
        return markBoxed(inspect(bigIntValueOf.call(obj)));
      }
      if (isBoolean(obj)) {
        return markBoxed(booleanValueOf.call(obj));
      }
      if (isString(obj)) {
        return markBoxed(inspect(String(obj)));
      }
      if (typeof window !== "undefined" && obj === window) {
        return "{ [object Window] }";
      }
      if (typeof globalThis !== "undefined" && obj === globalThis || typeof global !== "undefined" && obj === global) {
        return "{ [object globalThis] }";
      }
      if (!isDate(obj) && !isRegExp(obj)) {
        var ys = arrObjKeys(obj, inspect);
        var isPlainObject = gPO ? gPO(obj) === Object.prototype : obj instanceof Object || obj.constructor === Object;
        var protoTag = obj instanceof Object ? "" : "null prototype";
        var stringTag = !isPlainObject && toStringTag && Object(obj) === obj && toStringTag in obj ? $slice.call(toStr(obj), 8, -1) : protoTag ? "Object" : "";
        var constructorTag = isPlainObject || typeof obj.constructor !== "function" ? "" : obj.constructor.name ? obj.constructor.name + " " : "";
        var tag = constructorTag + (stringTag || protoTag ? "[" + $join.call($concat.call([], stringTag || [], protoTag || []), ": ") + "] " : "");
        if (ys.length === 0) {
          return tag + "{}";
        }
        if (indent) {
          return tag + "{" + indentedJoin(ys, indent) + "}";
        }
        return tag + "{ " + $join.call(ys, ", ") + " }";
      }
      return String(obj);
    };
    function wrapQuotes(s, defaultStyle, opts) {
      var style = opts.quoteStyle || defaultStyle;
      var quoteChar = quotes[style];
      return quoteChar + s + quoteChar;
    }
    function quote(s) {
      return $replace.call(String(s), /"/g, "&quot;");
    }
    function canTrustToString(obj) {
      return !toStringTag || !(typeof obj === "object" && (toStringTag in obj || typeof obj[toStringTag] !== "undefined"));
    }
    function isArray(obj) {
      return toStr(obj) === "[object Array]" && canTrustToString(obj);
    }
    function isDate(obj) {
      return toStr(obj) === "[object Date]" && canTrustToString(obj);
    }
    function isRegExp(obj) {
      return toStr(obj) === "[object RegExp]" && canTrustToString(obj);
    }
    function isError(obj) {
      return toStr(obj) === "[object Error]" && canTrustToString(obj);
    }
    function isString(obj) {
      return toStr(obj) === "[object String]" && canTrustToString(obj);
    }
    function isNumber(obj) {
      return toStr(obj) === "[object Number]" && canTrustToString(obj);
    }
    function isBoolean(obj) {
      return toStr(obj) === "[object Boolean]" && canTrustToString(obj);
    }
    function isSymbol(obj) {
      if (hasShammedSymbols) {
        return obj && typeof obj === "object" && obj instanceof Symbol;
      }
      if (typeof obj === "symbol") {
        return true;
      }
      if (!obj || typeof obj !== "object" || !symToString) {
        return false;
      }
      try {
        symToString.call(obj);
        return true;
      } catch (e) {
      }
      return false;
    }
    function isBigInt(obj) {
      if (!obj || typeof obj !== "object" || !bigIntValueOf) {
        return false;
      }
      try {
        bigIntValueOf.call(obj);
        return true;
      } catch (e) {
      }
      return false;
    }
    var hasOwn = Object.prototype.hasOwnProperty || function(key) {
      return key in this;
    };
    function has(obj, key) {
      return hasOwn.call(obj, key);
    }
    function toStr(obj) {
      return objectToString.call(obj);
    }
    function nameOf(f) {
      if (f.name) {
        return f.name;
      }
      var m = $match.call(functionToString.call(f), /^function\s*([\w$]+)/);
      if (m) {
        return m[1];
      }
      return null;
    }
    function indexOf(xs, x) {
      if (xs.indexOf) {
        return xs.indexOf(x);
      }
      for (var i = 0, l = xs.length; i < l; i++) {
        if (xs[i] === x) {
          return i;
        }
      }
      return -1;
    }
    function isMap(x) {
      if (!mapSize || !x || typeof x !== "object") {
        return false;
      }
      try {
        mapSize.call(x);
        try {
          setSize.call(x);
        } catch (s) {
          return true;
        }
        return x instanceof Map;
      } catch (e) {
      }
      return false;
    }
    function isWeakMap(x) {
      if (!weakMapHas || !x || typeof x !== "object") {
        return false;
      }
      try {
        weakMapHas.call(x, weakMapHas);
        try {
          weakSetHas.call(x, weakSetHas);
        } catch (s) {
          return true;
        }
        return x instanceof WeakMap;
      } catch (e) {
      }
      return false;
    }
    function isWeakRef(x) {
      if (!weakRefDeref || !x || typeof x !== "object") {
        return false;
      }
      try {
        weakRefDeref.call(x);
        return true;
      } catch (e) {
      }
      return false;
    }
    function isSet(x) {
      if (!setSize || !x || typeof x !== "object") {
        return false;
      }
      try {
        setSize.call(x);
        try {
          mapSize.call(x);
        } catch (m) {
          return true;
        }
        return x instanceof Set;
      } catch (e) {
      }
      return false;
    }
    function isWeakSet(x) {
      if (!weakSetHas || !x || typeof x !== "object") {
        return false;
      }
      try {
        weakSetHas.call(x, weakSetHas);
        try {
          weakMapHas.call(x, weakMapHas);
        } catch (s) {
          return true;
        }
        return x instanceof WeakSet;
      } catch (e) {
      }
      return false;
    }
    function isElement(x) {
      if (!x || typeof x !== "object") {
        return false;
      }
      if (typeof HTMLElement !== "undefined" && x instanceof HTMLElement) {
        return true;
      }
      return typeof x.nodeName === "string" && typeof x.getAttribute === "function";
    }
    function inspectString(str, opts) {
      if (str.length > opts.maxStringLength) {
        var remaining = str.length - opts.maxStringLength;
        var trailer = "... " + remaining + " more character" + (remaining > 1 ? "s" : "");
        return inspectString($slice.call(str, 0, opts.maxStringLength), opts) + trailer;
      }
      var quoteRE = quoteREs[opts.quoteStyle || "single"];
      quoteRE.lastIndex = 0;
      var s = $replace.call($replace.call(str, quoteRE, "\\$1"), /[\x00-\x1f]/g, lowbyte);
      return wrapQuotes(s, "single", opts);
    }
    function lowbyte(c) {
      var n = c.charCodeAt(0);
      var x = {
        8: "b",
        9: "t",
        10: "n",
        12: "f",
        13: "r"
      }[n];
      if (x) {
        return "\\" + x;
      }
      return "\\x" + (n < 16 ? "0" : "") + $toUpperCase.call(n.toString(16));
    }
    function markBoxed(str) {
      return "Object(" + str + ")";
    }
    function weakCollectionOf(type) {
      return type + " { ? }";
    }
    function collectionOf(type, size, entries, indent) {
      var joinedEntries = indent ? indentedJoin(entries, indent) : $join.call(entries, ", ");
      return type + " (" + size + ") {" + joinedEntries + "}";
    }
    function singleLineValues(xs) {
      for (var i = 0; i < xs.length; i++) {
        if (indexOf(xs[i], "\n") >= 0) {
          return false;
        }
      }
      return true;
    }
    function getIndent(opts, depth) {
      var baseIndent;
      if (opts.indent === "	") {
        baseIndent = "	";
      } else if (typeof opts.indent === "number" && opts.indent > 0) {
        baseIndent = $join.call(Array(opts.indent + 1), " ");
      } else {
        return null;
      }
      return {
        base: baseIndent,
        prev: $join.call(Array(depth + 1), baseIndent)
      };
    }
    function indentedJoin(xs, indent) {
      if (xs.length === 0) {
        return "";
      }
      var lineJoiner = "\n" + indent.prev + indent.base;
      return lineJoiner + $join.call(xs, "," + lineJoiner) + "\n" + indent.prev;
    }
    function arrObjKeys(obj, inspect) {
      var isArr = isArray(obj);
      var xs = [];
      if (isArr) {
        xs.length = obj.length;
        for (var i = 0; i < obj.length; i++) {
          xs[i] = has(obj, i) ? inspect(obj[i], obj) : "";
        }
      }
      var syms = typeof gOPS === "function" ? gOPS(obj) : [];
      var symMap;
      if (hasShammedSymbols) {
        symMap = {};
        for (var k = 0; k < syms.length; k++) {
          symMap["$" + syms[k]] = syms[k];
        }
      }
      for (var key in obj) {
        if (!has(obj, key)) {
          continue;
        }
        if (isArr && String(Number(key)) === key && key < obj.length) {
          continue;
        }
        if (hasShammedSymbols && symMap["$" + key] instanceof Symbol) {
          continue;
        } else if ($test.call(/[^\w$]/, key)) {
          xs.push(inspect(key, obj) + ": " + inspect(obj[key], obj));
        } else {
          xs.push(key + ": " + inspect(obj[key], obj));
        }
      }
      if (typeof gOPS === "function") {
        for (var j = 0; j < syms.length; j++) {
          if (isEnumerable.call(obj, syms[j])) {
            xs.push("[" + inspect(syms[j]) + "]: " + inspect(obj[syms[j]], obj));
          }
        }
      }
      return xs;
    }
  }
});

// node_modules/side-channel-list/index.js
var require_side_channel_list = __commonJS({
  "node_modules/side-channel-list/index.js"(exports, module) {
    "use strict";
    var inspect = require_object_inspect();
    var $TypeError = require_type();
    var listGetNode = function(list, key, isDelete) {
      var prev = list;
      var curr;
      for (; (curr = prev.next) != null; prev = curr) {
        if (curr.key === key) {
          prev.next = curr.next;
          if (!isDelete) {
            curr.next = /** @type {NonNullable<typeof list.next>} */
            list.next;
            list.next = curr;
          }
          return curr;
        }
      }
    };
    var listGet = function(objects, key) {
      if (!objects) {
        return void 0;
      }
      var node = listGetNode(objects, key);
      return node && node.value;
    };
    var listSet = function(objects, key, value) {
      var node = listGetNode(objects, key);
      if (node) {
        node.value = value;
      } else {
        objects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */
        {
          // eslint-disable-line no-param-reassign, no-extra-parens
          key,
          next: objects.next,
          value
        };
      }
    };
    var listHas = function(objects, key) {
      if (!objects) {
        return false;
      }
      return !!listGetNode(objects, key);
    };
    var listDelete = function(objects, key) {
      if (objects) {
        return listGetNode(objects, key, true);
      }
    };
    module.exports = function getSideChannelList() {
      var $o;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          var root = $o && $o.next;
          var deletedNode = listDelete($o, key);
          if (deletedNode && root && root === deletedNode) {
            $o = void 0;
          }
          return !!deletedNode;
        },
        get: function(key) {
          return listGet($o, key);
        },
        has: function(key) {
          return listHas($o, key);
        },
        set: function(key, value) {
          if (!$o) {
            $o = {
              next: void 0
            };
          }
          listSet(
            /** @type {NonNullable<typeof $o>} */
            $o,
            key,
            value
          );
        }
      };
      return channel;
    };
  }
});

// node_modules/side-channel-map/index.js
var require_side_channel_map = __commonJS({
  "node_modules/side-channel-map/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBound = require_call_bound();
    var inspect = require_object_inspect();
    var $TypeError = require_type();
    var $Map = GetIntrinsic("%Map%", true);
    var $mapGet = callBound("Map.prototype.get", true);
    var $mapSet = callBound("Map.prototype.set", true);
    var $mapHas = callBound("Map.prototype.has", true);
    var $mapDelete = callBound("Map.prototype.delete", true);
    var $mapSize = callBound("Map.prototype.size", true);
    module.exports = !!$Map && /** @type {Exclude<import('.'), false>} */
    function getSideChannelMap() {
      var $m;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          if ($m) {
            var result = $mapDelete($m, key);
            if ($mapSize($m) === 0) {
              $m = void 0;
            }
            return result;
          }
          return false;
        },
        get: function(key) {
          if ($m) {
            return $mapGet($m, key);
          }
        },
        has: function(key) {
          if ($m) {
            return $mapHas($m, key);
          }
          return false;
        },
        set: function(key, value) {
          if (!$m) {
            $m = new $Map();
          }
          $mapSet($m, key, value);
        }
      };
      return channel;
    };
  }
});

// node_modules/side-channel-weakmap/index.js
var require_side_channel_weakmap = __commonJS({
  "node_modules/side-channel-weakmap/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBound = require_call_bound();
    var inspect = require_object_inspect();
    var getSideChannelMap = require_side_channel_map();
    var $TypeError = require_type();
    var $WeakMap = GetIntrinsic("%WeakMap%", true);
    var $weakMapGet = callBound("WeakMap.prototype.get", true);
    var $weakMapSet = callBound("WeakMap.prototype.set", true);
    var $weakMapHas = callBound("WeakMap.prototype.has", true);
    var $weakMapDelete = callBound("WeakMap.prototype.delete", true);
    module.exports = $WeakMap ? (
      /** @type {Exclude<import('.'), false>} */
      function getSideChannelWeakMap() {
        var $wm;
        var $m;
        var channel = {
          assert: function(key) {
            if (!channel.has(key)) {
              throw new $TypeError("Side channel does not contain " + inspect(key));
            }
          },
          "delete": function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapDelete($wm, key);
              }
            } else if (getSideChannelMap) {
              if ($m) {
                return $m["delete"](key);
              }
            }
            return false;
          },
          get: function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapGet($wm, key);
              }
            }
            return $m && $m.get(key);
          },
          has: function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapHas($wm, key);
              }
            }
            return !!$m && $m.has(key);
          },
          set: function(key, value) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if (!$wm) {
                $wm = new $WeakMap();
              }
              $weakMapSet($wm, key, value);
            } else if (getSideChannelMap) {
              if (!$m) {
                $m = getSideChannelMap();
              }
              $m.set(key, value);
            }
          }
        };
        return channel;
      }
    ) : getSideChannelMap;
  }
});

// node_modules/side-channel/index.js
var require_side_channel = __commonJS({
  "node_modules/side-channel/index.js"(exports, module) {
    "use strict";
    var $TypeError = require_type();
    var inspect = require_object_inspect();
    var getSideChannelList = require_side_channel_list();
    var getSideChannelMap = require_side_channel_map();
    var getSideChannelWeakMap = require_side_channel_weakmap();
    var makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;
    module.exports = function getSideChannel() {
      var $channelData;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          return !!$channelData && $channelData["delete"](key);
        },
        get: function(key) {
          return $channelData && $channelData.get(key);
        },
        has: function(key) {
          return !!$channelData && $channelData.has(key);
        },
        set: function(key, value) {
          if (!$channelData) {
            $channelData = makeChannel();
          }
          $channelData.set(key, value);
        }
      };
      return channel;
    };
  }
});

// node_modules/qs/lib/formats.js
var require_formats = __commonJS({
  "node_modules/qs/lib/formats.js"(exports, module) {
    "use strict";
    var replace = String.prototype.replace;
    var percentTwenties = /%20/g;
    var Format = {
      RFC1738: "RFC1738",
      RFC3986: "RFC3986"
    };
    module.exports = {
      "default": Format.RFC3986,
      formatters: {
        RFC1738: function(value) {
          return replace.call(value, percentTwenties, "+");
        },
        RFC3986: function(value) {
          return String(value);
        }
      },
      RFC1738: Format.RFC1738,
      RFC3986: Format.RFC3986
    };
  }
});

// node_modules/qs/lib/utils.js
var require_utils = __commonJS({
  "node_modules/qs/lib/utils.js"(exports, module) {
    "use strict";
    var formats = require_formats();
    var has = Object.prototype.hasOwnProperty;
    var isArray = Array.isArray;
    var hexTable = function() {
      var array = [];
      for (var i = 0; i < 256; ++i) {
        array.push("%" + ((i < 16 ? "0" : "") + i.toString(16)).toUpperCase());
      }
      return array;
    }();
    var compactQueue = function compactQueue2(queue) {
      while (queue.length > 1) {
        var item = queue.pop();
        var obj = item.obj[item.prop];
        if (isArray(obj)) {
          var compacted = [];
          for (var j = 0; j < obj.length; ++j) {
            if (typeof obj[j] !== "undefined") {
              compacted.push(obj[j]);
            }
          }
          item.obj[item.prop] = compacted;
        }
      }
    };
    var arrayToObject = function arrayToObject2(source, options) {
      var obj = options && options.plainObjects ? { __proto__: null } : {};
      for (var i = 0; i < source.length; ++i) {
        if (typeof source[i] !== "undefined") {
          obj[i] = source[i];
        }
      }
      return obj;
    };
    var merge = function merge2(target, source, options) {
      if (!source) {
        return target;
      }
      if (typeof source !== "object" && typeof source !== "function") {
        if (isArray(target)) {
          target.push(source);
        } else if (target && typeof target === "object") {
          if (options && (options.plainObjects || options.allowPrototypes) || !has.call(Object.prototype, source)) {
            target[source] = true;
          }
        } else {
          return [target, source];
        }
        return target;
      }
      if (!target || typeof target !== "object") {
        return [target].concat(source);
      }
      var mergeTarget = target;
      if (isArray(target) && !isArray(source)) {
        mergeTarget = arrayToObject(target, options);
      }
      if (isArray(target) && isArray(source)) {
        source.forEach(function(item, i) {
          if (has.call(target, i)) {
            var targetItem = target[i];
            if (targetItem && typeof targetItem === "object" && item && typeof item === "object") {
              target[i] = merge2(targetItem, item, options);
            } else {
              target.push(item);
            }
          } else {
            target[i] = item;
          }
        });
        return target;
      }
      return Object.keys(source).reduce(function(acc, key) {
        var value = source[key];
        if (has.call(acc, key)) {
          acc[key] = merge2(acc[key], value, options);
        } else {
          acc[key] = value;
        }
        return acc;
      }, mergeTarget);
    };
    var assign = function assignSingleSource(target, source) {
      return Object.keys(source).reduce(function(acc, key) {
        acc[key] = source[key];
        return acc;
      }, target);
    };
    var decode = function(str, defaultDecoder, charset) {
      var strWithoutPlus = str.replace(/\+/g, " ");
      if (charset === "iso-8859-1") {
        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);
      }
      try {
        return decodeURIComponent(strWithoutPlus);
      } catch (e) {
        return strWithoutPlus;
      }
    };
    var limit = 1024;
    var encode = function encode2(str, defaultEncoder, charset, kind, format) {
      if (str.length === 0) {
        return str;
      }
      var string = str;
      if (typeof str === "symbol") {
        string = Symbol.prototype.toString.call(str);
      } else if (typeof str !== "string") {
        string = String(str);
      }
      if (charset === "iso-8859-1") {
        return escape(string).replace(/%u[0-9a-f]{4}/gi, function($0) {
          return "%26%23" + parseInt($0.slice(2), 16) + "%3B";
        });
      }
      var out = "";
      for (var j = 0; j < string.length; j += limit) {
        var segment = string.length >= limit ? string.slice(j, j + limit) : string;
        var arr = [];
        for (var i = 0; i < segment.length; ++i) {
          var c = segment.charCodeAt(i);
          if (c === 45 || c === 46 || c === 95 || c === 126 || c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || format === formats.RFC1738 && (c === 40 || c === 41)) {
            arr[arr.length] = segment.charAt(i);
            continue;
          }
          if (c < 128) {
            arr[arr.length] = hexTable[c];
            continue;
          }
          if (c < 2048) {
            arr[arr.length] = hexTable[192 | c >> 6] + hexTable[128 | c & 63];
            continue;
          }
          if (c < 55296 || c >= 57344) {
            arr[arr.length] = hexTable[224 | c >> 12] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
            continue;
          }
          i += 1;
          c = 65536 + ((c & 1023) << 10 | segment.charCodeAt(i) & 1023);
          arr[arr.length] = hexTable[240 | c >> 18] + hexTable[128 | c >> 12 & 63] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
        }
        out += arr.join("");
      }
      return out;
    };
    var compact = function compact2(value) {
      var queue = [{ obj: { o: value }, prop: "o" }];
      var refs = [];
      for (var i = 0; i < queue.length; ++i) {
        var item = queue[i];
        var obj = item.obj[item.prop];
        var keys = Object.keys(obj);
        for (var j = 0; j < keys.length; ++j) {
          var key = keys[j];
          var val = obj[key];
          if (typeof val === "object" && val !== null && refs.indexOf(val) === -1) {
            queue.push({ obj, prop: key });
            refs.push(val);
          }
        }
      }
      compactQueue(queue);
      return value;
    };
    var isRegExp = function isRegExp2(obj) {
      return Object.prototype.toString.call(obj) === "[object RegExp]";
    };
    var isBuffer = function isBuffer2(obj) {
      if (!obj || typeof obj !== "object") {
        return false;
      }
      return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));
    };
    var combine = function combine2(a, b) {
      return [].concat(a, b);
    };
    var maybeMap = function maybeMap2(val, fn) {
      if (isArray(val)) {
        var mapped = [];
        for (var i = 0; i < val.length; i += 1) {
          mapped.push(fn(val[i]));
        }
        return mapped;
      }
      return fn(val);
    };
    module.exports = {
      arrayToObject,
      assign,
      combine,
      compact,
      decode,
      encode,
      isBuffer,
      isRegExp,
      maybeMap,
      merge
    };
  }
});

// node_modules/qs/lib/stringify.js
var require_stringify = __commonJS({
  "node_modules/qs/lib/stringify.js"(exports, module) {
    "use strict";
    var getSideChannel = require_side_channel();
    var utils = require_utils();
    var formats = require_formats();
    var has = Object.prototype.hasOwnProperty;
    var arrayPrefixGenerators = {
      brackets: function brackets(prefix) {
        return prefix + "[]";
      },
      comma: "comma",
      indices: function indices(prefix, key) {
        return prefix + "[" + key + "]";
      },
      repeat: function repeat(prefix) {
        return prefix;
      }
    };
    var isArray = Array.isArray;
    var push = Array.prototype.push;
    var pushToArray = function(arr, valueOrArray) {
      push.apply(arr, isArray(valueOrArray) ? valueOrArray : [valueOrArray]);
    };
    var toISO = Date.prototype.toISOString;
    var defaultFormat = formats["default"];
    var defaults = {
      addQueryPrefix: false,
      allowDots: false,
      allowEmptyArrays: false,
      arrayFormat: "indices",
      charset: "utf-8",
      charsetSentinel: false,
      commaRoundTrip: false,
      delimiter: "&",
      encode: true,
      encodeDotInKeys: false,
      encoder: utils.encode,
      encodeValuesOnly: false,
      filter: void 0,
      format: defaultFormat,
      formatter: formats.formatters[defaultFormat],
      // deprecated
      indices: false,
      serializeDate: function serializeDate(date) {
        return toISO.call(date);
      },
      skipNulls: false,
      strictNullHandling: false
    };
    var isNonNullishPrimitive = function isNonNullishPrimitive2(v) {
      return typeof v === "string" || typeof v === "number" || typeof v === "boolean" || typeof v === "symbol" || typeof v === "bigint";
    };
    var sentinel = {};
    var stringify = function stringify2(object, prefix, generateArrayPrefix, commaRoundTrip, allowEmptyArrays, strictNullHandling, skipNulls, encodeDotInKeys, encoder, filter, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, sideChannel) {
      var obj = object;
      var tmpSc = sideChannel;
      var step = 0;
      var findFlag = false;
      while ((tmpSc = tmpSc.get(sentinel)) !== void 0 && !findFlag) {
        var pos = tmpSc.get(object);
        step += 1;
        if (typeof pos !== "undefined") {
          if (pos === step) {
            throw new RangeError("Cyclic object value");
          } else {
            findFlag = true;
          }
        }
        if (typeof tmpSc.get(sentinel) === "undefined") {
          step = 0;
        }
      }
      if (typeof filter === "function") {
        obj = filter(prefix, obj);
      } else if (obj instanceof Date) {
        obj = serializeDate(obj);
      } else if (generateArrayPrefix === "comma" && isArray(obj)) {
        obj = utils.maybeMap(obj, function(value2) {
          if (value2 instanceof Date) {
            return serializeDate(value2);
          }
          return value2;
        });
      }
      if (obj === null) {
        if (strictNullHandling) {
          return encoder && !encodeValuesOnly ? encoder(prefix, defaults.encoder, charset, "key", format) : prefix;
        }
        obj = "";
      }
      if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {
        if (encoder) {
          var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults.encoder, charset, "key", format);
          return [formatter(keyValue) + "=" + formatter(encoder(obj, defaults.encoder, charset, "value", format))];
        }
        return [formatter(prefix) + "=" + formatter(String(obj))];
      }
      var values = [];
      if (typeof obj === "undefined") {
        return values;
      }
      var objKeys;
      if (generateArrayPrefix === "comma" && isArray(obj)) {
        if (encodeValuesOnly && encoder) {
          obj = utils.maybeMap(obj, encoder);
        }
        objKeys = [{ value: obj.length > 0 ? obj.join(",") || null : void 0 }];
      } else if (isArray(filter)) {
        objKeys = filter;
      } else {
        var keys = Object.keys(obj);
        objKeys = sort ? keys.sort(sort) : keys;
      }
      var encodedPrefix = encodeDotInKeys ? String(prefix).replace(/\./g, "%2E") : String(prefix);
      var adjustedPrefix = commaRoundTrip && isArray(obj) && obj.length === 1 ? encodedPrefix + "[]" : encodedPrefix;
      if (allowEmptyArrays && isArray(obj) && obj.length === 0) {
        return adjustedPrefix + "[]";
      }
      for (var j = 0; j < objKeys.length; ++j) {
        var key = objKeys[j];
        var value = typeof key === "object" && key && typeof key.value !== "undefined" ? key.value : obj[key];
        if (skipNulls && value === null) {
          continue;
        }
        var encodedKey = allowDots && encodeDotInKeys ? String(key).replace(/\./g, "%2E") : String(key);
        var keyPrefix = isArray(obj) ? typeof generateArrayPrefix === "function" ? generateArrayPrefix(adjustedPrefix, encodedKey) : adjustedPrefix : adjustedPrefix + (allowDots ? "." + encodedKey : "[" + encodedKey + "]");
        sideChannel.set(object, step);
        var valueSideChannel = getSideChannel();
        valueSideChannel.set(sentinel, sideChannel);
        pushToArray(values, stringify2(
          value,
          keyPrefix,
          generateArrayPrefix,
          commaRoundTrip,
          allowEmptyArrays,
          strictNullHandling,
          skipNulls,
          encodeDotInKeys,
          generateArrayPrefix === "comma" && encodeValuesOnly && isArray(obj) ? null : encoder,
          filter,
          sort,
          allowDots,
          serializeDate,
          format,
          formatter,
          encodeValuesOnly,
          charset,
          valueSideChannel
        ));
      }
      return values;
    };
    var normalizeStringifyOptions = function normalizeStringifyOptions2(opts) {
      if (!opts) {
        return defaults;
      }
      if (typeof opts.allowEmptyArrays !== "undefined" && typeof opts.allowEmptyArrays !== "boolean") {
        throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");
      }
      if (typeof opts.encodeDotInKeys !== "undefined" && typeof opts.encodeDotInKeys !== "boolean") {
        throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");
      }
      if (opts.encoder !== null && typeof opts.encoder !== "undefined" && typeof opts.encoder !== "function") {
        throw new TypeError("Encoder has to be a function.");
      }
      var charset = opts.charset || defaults.charset;
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      var format = formats["default"];
      if (typeof opts.format !== "undefined") {
        if (!has.call(formats.formatters, opts.format)) {
          throw new TypeError("Unknown format option provided.");
        }
        format = opts.format;
      }
      var formatter = formats.formatters[format];
      var filter = defaults.filter;
      if (typeof opts.filter === "function" || isArray(opts.filter)) {
        filter = opts.filter;
      }
      var arrayFormat;
      if (opts.arrayFormat in arrayPrefixGenerators) {
        arrayFormat = opts.arrayFormat;
      } else if ("indices" in opts) {
        arrayFormat = opts.indices ? "indices" : "repeat";
      } else {
        arrayFormat = defaults.arrayFormat;
      }
      if ("commaRoundTrip" in opts && typeof opts.commaRoundTrip !== "boolean") {
        throw new TypeError("`commaRoundTrip` must be a boolean, or absent");
      }
      var allowDots = typeof opts.allowDots === "undefined" ? opts.encodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;
      return {
        addQueryPrefix: typeof opts.addQueryPrefix === "boolean" ? opts.addQueryPrefix : defaults.addQueryPrefix,
        allowDots,
        allowEmptyArrays: typeof opts.allowEmptyArrays === "boolean" ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,
        arrayFormat,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults.charsetSentinel,
        commaRoundTrip: !!opts.commaRoundTrip,
        delimiter: typeof opts.delimiter === "undefined" ? defaults.delimiter : opts.delimiter,
        encode: typeof opts.encode === "boolean" ? opts.encode : defaults.encode,
        encodeDotInKeys: typeof opts.encodeDotInKeys === "boolean" ? opts.encodeDotInKeys : defaults.encodeDotInKeys,
        encoder: typeof opts.encoder === "function" ? opts.encoder : defaults.encoder,
        encodeValuesOnly: typeof opts.encodeValuesOnly === "boolean" ? opts.encodeValuesOnly : defaults.encodeValuesOnly,
        filter,
        format,
        formatter,
        serializeDate: typeof opts.serializeDate === "function" ? opts.serializeDate : defaults.serializeDate,
        skipNulls: typeof opts.skipNulls === "boolean" ? opts.skipNulls : defaults.skipNulls,
        sort: typeof opts.sort === "function" ? opts.sort : null,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults.strictNullHandling
      };
    };
    module.exports = function(object, opts) {
      var obj = object;
      var options = normalizeStringifyOptions(opts);
      var objKeys;
      var filter;
      if (typeof options.filter === "function") {
        filter = options.filter;
        obj = filter("", obj);
      } else if (isArray(options.filter)) {
        filter = options.filter;
        objKeys = filter;
      }
      var keys = [];
      if (typeof obj !== "object" || obj === null) {
        return "";
      }
      var generateArrayPrefix = arrayPrefixGenerators[options.arrayFormat];
      var commaRoundTrip = generateArrayPrefix === "comma" && options.commaRoundTrip;
      if (!objKeys) {
        objKeys = Object.keys(obj);
      }
      if (options.sort) {
        objKeys.sort(options.sort);
      }
      var sideChannel = getSideChannel();
      for (var i = 0; i < objKeys.length; ++i) {
        var key = objKeys[i];
        var value = obj[key];
        if (options.skipNulls && value === null) {
          continue;
        }
        pushToArray(keys, stringify(
          value,
          key,
          generateArrayPrefix,
          commaRoundTrip,
          options.allowEmptyArrays,
          options.strictNullHandling,
          options.skipNulls,
          options.encodeDotInKeys,
          options.encode ? options.encoder : null,
          options.filter,
          options.sort,
          options.allowDots,
          options.serializeDate,
          options.format,
          options.formatter,
          options.encodeValuesOnly,
          options.charset,
          sideChannel
        ));
      }
      var joined = keys.join(options.delimiter);
      var prefix = options.addQueryPrefix === true ? "?" : "";
      if (options.charsetSentinel) {
        if (options.charset === "iso-8859-1") {
          prefix += "utf8=%26%2310003%3B&";
        } else {
          prefix += "utf8=%E2%9C%93&";
        }
      }
      return joined.length > 0 ? prefix + joined : "";
    };
  }
});

// node_modules/qs/lib/parse.js
var require_parse = __commonJS({
  "node_modules/qs/lib/parse.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var has = Object.prototype.hasOwnProperty;
    var isArray = Array.isArray;
    var defaults = {
      allowDots: false,
      allowEmptyArrays: false,
      allowPrototypes: false,
      allowSparse: false,
      arrayLimit: 20,
      charset: "utf-8",
      charsetSentinel: false,
      comma: false,
      decodeDotInKeys: false,
      decoder: utils.decode,
      delimiter: "&",
      depth: 5,
      duplicates: "combine",
      ignoreQueryPrefix: false,
      interpretNumericEntities: false,
      parameterLimit: 1e3,
      parseArrays: true,
      plainObjects: false,
      strictDepth: false,
      strictNullHandling: false,
      throwOnLimitExceeded: false
    };
    var interpretNumericEntities = function(str) {
      return str.replace(/&#(\d+);/g, function($0, numberStr) {
        return String.fromCharCode(parseInt(numberStr, 10));
      });
    };
    var parseArrayValue = function(val, options, currentArrayLength) {
      if (val && typeof val === "string" && options.comma && val.indexOf(",") > -1) {
        return val.split(",");
      }
      if (options.throwOnLimitExceeded && currentArrayLength >= options.arrayLimit) {
        throw new RangeError("Array limit exceeded. Only " + options.arrayLimit + " element" + (options.arrayLimit === 1 ? "" : "s") + " allowed in an array.");
      }
      return val;
    };
    var isoSentinel = "utf8=%26%2310003%3B";
    var charsetSentinel = "utf8=%E2%9C%93";
    var parseValues = function parseQueryStringValues(str, options) {
      var obj = { __proto__: null };
      var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\?/, "") : str;
      cleanStr = cleanStr.replace(/%5B/gi, "[").replace(/%5D/gi, "]");
      var limit = options.parameterLimit === Infinity ? void 0 : options.parameterLimit;
      var parts = cleanStr.split(
        options.delimiter,
        options.throwOnLimitExceeded ? limit + 1 : limit
      );
      if (options.throwOnLimitExceeded && parts.length > limit) {
        throw new RangeError("Parameter limit exceeded. Only " + limit + " parameter" + (limit === 1 ? "" : "s") + " allowed.");
      }
      var skipIndex = -1;
      var i;
      var charset = options.charset;
      if (options.charsetSentinel) {
        for (i = 0; i < parts.length; ++i) {
          if (parts[i].indexOf("utf8=") === 0) {
            if (parts[i] === charsetSentinel) {
              charset = "utf-8";
            } else if (parts[i] === isoSentinel) {
              charset = "iso-8859-1";
            }
            skipIndex = i;
            i = parts.length;
          }
        }
      }
      for (i = 0; i < parts.length; ++i) {
        if (i === skipIndex) {
          continue;
        }
        var part = parts[i];
        var bracketEqualsPos = part.indexOf("]=");
        var pos = bracketEqualsPos === -1 ? part.indexOf("=") : bracketEqualsPos + 1;
        var key;
        var val;
        if (pos === -1) {
          key = options.decoder(part, defaults.decoder, charset, "key");
          val = options.strictNullHandling ? null : "";
        } else {
          key = options.decoder(part.slice(0, pos), defaults.decoder, charset, "key");
          val = utils.maybeMap(
            parseArrayValue(
              part.slice(pos + 1),
              options,
              isArray(obj[key]) ? obj[key].length : 0
            ),
            function(encodedVal) {
              return options.decoder(encodedVal, defaults.decoder, charset, "value");
            }
          );
        }
        if (val && options.interpretNumericEntities && charset === "iso-8859-1") {
          val = interpretNumericEntities(String(val));
        }
        if (part.indexOf("[]=") > -1) {
          val = isArray(val) ? [val] : val;
        }
        var existing = has.call(obj, key);
        if (existing && options.duplicates === "combine") {
          obj[key] = utils.combine(obj[key], val);
        } else if (!existing || options.duplicates === "last") {
          obj[key] = val;
        }
      }
      return obj;
    };
    var parseObject = function(chain, val, options, valuesParsed) {
      var currentArrayLength = 0;
      if (chain.length > 0 && chain[chain.length - 1] === "[]") {
        var parentKey = chain.slice(0, -1).join("");
        currentArrayLength = Array.isArray(val) && val[parentKey] ? val[parentKey].length : 0;
      }
      var leaf = valuesParsed ? val : parseArrayValue(val, options, currentArrayLength);
      for (var i = chain.length - 1; i >= 0; --i) {
        var obj;
        var root = chain[i];
        if (root === "[]" && options.parseArrays) {
          obj = options.allowEmptyArrays && (leaf === "" || options.strictNullHandling && leaf === null) ? [] : utils.combine([], leaf);
        } else {
          obj = options.plainObjects ? { __proto__: null } : {};
          var cleanRoot = root.charAt(0) === "[" && root.charAt(root.length - 1) === "]" ? root.slice(1, -1) : root;
          var decodedRoot = options.decodeDotInKeys ? cleanRoot.replace(/%2E/g, ".") : cleanRoot;
          var index = parseInt(decodedRoot, 10);
          if (!options.parseArrays && decodedRoot === "") {
            obj = { 0: leaf };
          } else if (!isNaN(index) && root !== decodedRoot && String(index) === decodedRoot && index >= 0 && (options.parseArrays && index <= options.arrayLimit)) {
            obj = [];
            obj[index] = leaf;
          } else if (decodedRoot !== "__proto__") {
            obj[decodedRoot] = leaf;
          }
        }
        leaf = obj;
      }
      return leaf;
    };
    var parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {
      if (!givenKey) {
        return;
      }
      var key = options.allowDots ? givenKey.replace(/\.([^.[]+)/g, "[$1]") : givenKey;
      var brackets = /(\[[^[\]]*])/;
      var child = /(\[[^[\]]*])/g;
      var segment = options.depth > 0 && brackets.exec(key);
      var parent = segment ? key.slice(0, segment.index) : key;
      var keys = [];
      if (parent) {
        if (!options.plainObjects && has.call(Object.prototype, parent)) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys.push(parent);
      }
      var i = 0;
      while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {
        i += 1;
        if (!options.plainObjects && has.call(Object.prototype, segment[1].slice(1, -1))) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys.push(segment[1]);
      }
      if (segment) {
        if (options.strictDepth === true) {
          throw new RangeError("Input depth exceeded depth option of " + options.depth + " and strictDepth is true");
        }
        keys.push("[" + key.slice(segment.index) + "]");
      }
      return parseObject(keys, val, options, valuesParsed);
    };
    var normalizeParseOptions = function normalizeParseOptions2(opts) {
      if (!opts) {
        return defaults;
      }
      if (typeof opts.allowEmptyArrays !== "undefined" && typeof opts.allowEmptyArrays !== "boolean") {
        throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");
      }
      if (typeof opts.decodeDotInKeys !== "undefined" && typeof opts.decodeDotInKeys !== "boolean") {
        throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");
      }
      if (opts.decoder !== null && typeof opts.decoder !== "undefined" && typeof opts.decoder !== "function") {
        throw new TypeError("Decoder has to be a function.");
      }
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      if (typeof opts.throwOnLimitExceeded !== "undefined" && typeof opts.throwOnLimitExceeded !== "boolean") {
        throw new TypeError("`throwOnLimitExceeded` option must be a boolean");
      }
      var charset = typeof opts.charset === "undefined" ? defaults.charset : opts.charset;
      var duplicates = typeof opts.duplicates === "undefined" ? defaults.duplicates : opts.duplicates;
      if (duplicates !== "combine" && duplicates !== "first" && duplicates !== "last") {
        throw new TypeError("The duplicates option must be either combine, first, or last");
      }
      var allowDots = typeof opts.allowDots === "undefined" ? opts.decodeDotInKeys === true ? true : defaults.allowDots : !!opts.allowDots;
      return {
        allowDots,
        allowEmptyArrays: typeof opts.allowEmptyArrays === "boolean" ? !!opts.allowEmptyArrays : defaults.allowEmptyArrays,
        allowPrototypes: typeof opts.allowPrototypes === "boolean" ? opts.allowPrototypes : defaults.allowPrototypes,
        allowSparse: typeof opts.allowSparse === "boolean" ? opts.allowSparse : defaults.allowSparse,
        arrayLimit: typeof opts.arrayLimit === "number" ? opts.arrayLimit : defaults.arrayLimit,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults.charsetSentinel,
        comma: typeof opts.comma === "boolean" ? opts.comma : defaults.comma,
        decodeDotInKeys: typeof opts.decodeDotInKeys === "boolean" ? opts.decodeDotInKeys : defaults.decodeDotInKeys,
        decoder: typeof opts.decoder === "function" ? opts.decoder : defaults.decoder,
        delimiter: typeof opts.delimiter === "string" || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults.delimiter,
        // eslint-disable-next-line no-implicit-coercion, no-extra-parens
        depth: typeof opts.depth === "number" || opts.depth === false ? +opts.depth : defaults.depth,
        duplicates,
        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,
        interpretNumericEntities: typeof opts.interpretNumericEntities === "boolean" ? opts.interpretNumericEntities : defaults.interpretNumericEntities,
        parameterLimit: typeof opts.parameterLimit === "number" ? opts.parameterLimit : defaults.parameterLimit,
        parseArrays: opts.parseArrays !== false,
        plainObjects: typeof opts.plainObjects === "boolean" ? opts.plainObjects : defaults.plainObjects,
        strictDepth: typeof opts.strictDepth === "boolean" ? !!opts.strictDepth : defaults.strictDepth,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults.strictNullHandling,
        throwOnLimitExceeded: typeof opts.throwOnLimitExceeded === "boolean" ? opts.throwOnLimitExceeded : false
      };
    };
    module.exports = function(str, opts) {
      var options = normalizeParseOptions(opts);
      if (str === "" || str === null || typeof str === "undefined") {
        return options.plainObjects ? { __proto__: null } : {};
      }
      var tempObj = typeof str === "string" ? parseValues(str, options) : str;
      var obj = options.plainObjects ? { __proto__: null } : {};
      var keys = Object.keys(tempObj);
      for (var i = 0; i < keys.length; ++i) {
        var key = keys[i];
        var newObj = parseKeys(key, tempObj[key], options, typeof str === "string");
        obj = utils.merge(obj, newObj, options);
      }
      if (options.allowSparse === true) {
        return obj;
      }
      return utils.compact(obj);
    };
  }
});

// node_modules/qs/lib/index.js
var require_lib = __commonJS({
  "node_modules/qs/lib/index.js"(exports, module) {
    "use strict";
    var stringify = require_stringify();
    var parse = require_parse();
    var formats = require_formats();
    module.exports = {
      formats,
      parse,
      stringify
    };
  }
});

// node_modules/ra-supabase-core/src/getSearchString.ts
function getSearchString() {
  const search = window.location.search;
  const hash = window.location.hash.substring(1);
  return search && search !== "" ? search : hash.includes("?") ? hash.split("?")[1] : hash;
}

// node_modules/ra-supabase-core/src/authProvider.ts
var supabaseAuthProvider = (client, { getIdentity, getPermissions, redirectTo }) => {
  const authProvider = {
    async login(params) {
      const emailPasswordParams = params;
      if (emailPasswordParams.email && emailPasswordParams.password) {
        const { error } = await client.auth.signInWithPassword(
          emailPasswordParams
        );
        if (error) {
          throw error;
        }
        return;
      }
      const oauthParams = params;
      if (oauthParams.provider) {
        client.auth.signInWithOAuth({
          ...oauthParams,
          options: { redirectTo }
        });
        return Promise.reject();
      }
      return Promise.reject(new Error("Invalid login parameters"));
    },
    async setPassword({
      access_token,
      refresh_token,
      password
    }) {
      const { error: sessionError } = await client.auth.setSession({
        access_token,
        refresh_token
      });
      if (sessionError) {
        throw sessionError;
      }
      const { error } = await client.auth.updateUser({
        password
      });
      if (error) {
        throw error;
      }
      return void 0;
    },
    async resetPassword(params) {
      const { email, ...options } = params;
      const { error } = await client.auth.resetPasswordForEmail(
        email,
        options
      );
      if (error) {
        throw error;
      }
      return void 0;
    },
    async logout() {
      const { error } = await client.auth.signOut();
      if (error) {
        throw error;
      }
    },
    async checkError(error) {
      if (error.status === 401 || error.status === 403 || // Supabase returns 400 when the session is missing, we need to check this case too.
      error.status === 400 && error.name === "AuthSessionMissingError") {
        return Promise.reject();
      }
      return Promise.resolve();
    },
    async handleCallback() {
      const { access_token, refresh_token, type } = getUrlParams();
      if (type === "recovery" || type === "invite") {
        if (access_token && refresh_token) {
          return {
            redirectTo: () => ({
              pathname: redirectTo ? `${redirectTo}/set-password` : "/set-password",
              search: `access_token=${access_token}&refresh_token=${refresh_token}&type=${type}`
            })
          };
        }
        if (true) {
          console.error(
            "Missing access_token or refresh_token for an invite or recovery"
          );
        }
      }
    },
    async checkAuth() {
      if (window.location.pathname === "/set-password" || window.location.hash.includes("#/set-password")) {
        return;
      }
      if (window.location.pathname === "/forgot-password" || window.location.hash.includes("#/forgot-password")) {
        return;
      }
      const { access_token, refresh_token, type } = getUrlParams();
      if (type === "recovery" || type === "invite") {
        if (access_token && refresh_token) {
          throw {
            redirectTo: () => ({
              pathname: redirectTo ? `${redirectTo}/set-password` : "/set-password",
              search: `access_token=${access_token}&refresh_token=${refresh_token}&type=${type}`
            }),
            message: false
          };
        }
        if (true) {
          console.error(
            "Missing access_token or refresh_token for an invite or recovery"
          );
        }
      }
      const { data } = await client.auth.getSession();
      if (data.session == null) {
        return Promise.reject();
      }
      return Promise.resolve();
    },
    async getPermissions() {
      if (typeof getPermissions !== "function") {
        return;
      }
      if (window.location.pathname === "/set-password" || window.location.hash.includes("#/set-password") || window.location.pathname === "/forgot-password" || window.location.hash.includes("#/forgot-password")) {
        return;
      }
      const { data, error } = await client.auth.getUser();
      if (error || data.user == null) {
        return;
      }
      const permissions = await getPermissions(data.user);
      return permissions;
    }
  };
  if (typeof getIdentity === "function") {
    authProvider.getIdentity = async () => {
      const { data } = await client.auth.getUser();
      if (data.user == null) {
        throw new Error();
      }
      const identity = await getIdentity(data.user);
      return identity;
    };
  }
  return authProvider;
};
var getUrlParams = () => {
  const searchStr = getSearchString();
  const urlSearchParams = new URLSearchParams(searchStr);
  const access_token = urlSearchParams.get("access_token");
  const refresh_token = urlSearchParams.get("refresh_token");
  const type = urlSearchParams.get("type");
  return { access_token, refresh_token, type };
};

// node_modules/@raphiniert/ra-data-postgrest/esm/urlBuilder.js
var __assign = function() {
  __assign = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign.apply(this, arguments);
};
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
    t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
};
var isObject = function(obj) {
  return typeof obj === "object" && !Array.isArray(obj) && obj !== null;
};
var resolveKeys = function(filter, keys) {
  var result = filter[keys[0]];
  for (var i = 1; i < keys.length; ++i) {
    result = result[keys[i]];
  }
  return result;
};
var parseFilters = function(params, defaultListOp) {
  var filter = params.filter, _a = params.meta, meta = _a === void 0 ? {} : _a;
  var result = {};
  result.filter = {};
  Object.keys(filter).forEach(function(key) {
    var keyArray = [key];
    var values;
    if (key.split("@")[0] !== "" && isObject(filter[key])) {
      var innerVal = filter[key];
      do {
        var inner = resolveKeys(filter, keyArray);
        var innerKey = Object.keys(inner)[0];
        keyArray.push(innerKey);
        innerVal = inner[innerKey];
      } while (isObject(innerVal));
      key = keyArray.join(".");
      values = [innerVal];
    } else {
      values = [filter[key]];
    }
    var splitKey = key.split("@");
    var operation = splitKey.length == 2 ? splitKey[1] : defaultListOp;
    if (["like", "ilike"].includes(operation)) {
      values = resolveKeys(filter, keyArray).trim().split(/\s+/);
    } else if (["or", "and"].includes(operation)) {
      var subFilter = parseFilters({ filter: resolveKeys(filter, keyArray) }, defaultListOp).filter;
      var filterExpressions_1 = [];
      Object.entries(subFilter).forEach(function(_a2) {
        var op = _a2[0], val = _a2[1];
        if (Array.isArray(val))
          filterExpressions_1.push.apply(filterExpressions_1, val.map(function(v) {
            return [op, v].join(".");
          }));
        else
          filterExpressions_1.push([op, val].join("."));
      });
      values = ["(".concat(filterExpressions_1.join(","), ")")];
    }
    values.forEach(function(value) {
      var op = function() {
        if (operation.length === 0)
          return "".concat(value);
        if (operation.includes("like"))
          return "".concat(operation, ".*").concat(value, "*");
        if (["and", "or"].includes(operation))
          return "".concat(value);
        return "".concat(operation, ".").concat(value);
      }();
      if (result.filter[splitKey[0]] === void 0) {
        if (["and", "or"].includes(operation)) {
          result.filter[operation] = op;
        } else {
          result.filter[splitKey[0]] = op;
        }
      } else {
        if (!Array.isArray(result[splitKey[0]])) {
          result.filter[splitKey[0]] = [
            result.filter[splitKey[0]],
            op
          ];
        } else {
          result.filter[splitKey[0]].push(op);
        }
      }
    });
  });
  if (meta === null || meta === void 0 ? void 0 : meta.columns) {
    result.select = Array.isArray(meta.columns) ? meta.columns.join(",") : meta.columns;
  }
  return result;
};
var getPrimaryKey = function(resource, primaryKeys) {
  return primaryKeys && primaryKeys.get(resource) || ["id"];
};
var decodeId = function(id, primaryKey) {
  if (isCompoundKey(primaryKey)) {
    return JSON.parse(id.toString());
  } else {
    return [id.toString()];
  }
};
var encodeId = function(data, primaryKey) {
  if (isCompoundKey(primaryKey)) {
    return JSON.stringify(primaryKey.map(function(key) {
      return data[key];
    }));
  } else {
    return data[primaryKey[0]];
  }
};
var removePrimaryKey = function(data, primaryKey) {
  var newData = __assign({}, data);
  primaryKey.forEach(function(key) {
    delete newData[key];
  });
  return newData;
};
var dataWithVirtualId = function(data, primaryKey) {
  if (primaryKey.length === 1 && primaryKey[0] === "id") {
    return data;
  }
  return Object.assign(data, {
    id: encodeId(data, primaryKey)
  });
};
var dataWithoutVirtualId = function(data, primaryKey) {
  if (primaryKey.length === 1 && primaryKey[0] === "id") {
    return data;
  }
  var id = data.id, dataWithoutId = __rest(data, ["id"]);
  return dataWithoutId;
};
var isCompoundKey = function(primaryKey) {
  return primaryKey.length > 1;
};
var getQuery = function(primaryKey, ids, resource, meta) {
  var _a, _b;
  if (meta === void 0) {
    meta = null;
  }
  var result = {};
  if (Array.isArray(ids) && ids.length > 1) {
    if (resource.startsWith("rpc/")) {
      console.error("PostgREST's rpc endpoints are not intended to be handled as views. Therefore, no query generation for multiple key values implemented!");
      return;
    }
    if (isCompoundKey(primaryKey)) {
      result = {
        or: "(".concat(ids.map(function(id2) {
          var primaryKeyParams = decodeId(id2, primaryKey);
          return "and(".concat(primaryKey.map(function(key, i) {
            return "".concat(key, ".eq.").concat(primaryKeyParams[i]);
          }).join(","), ")");
        }), ")")
      };
    } else {
      result = (_a = {}, _a[primaryKey[0]] = "in.(".concat(ids.join(","), ")"), _a);
    }
  } else if (ids) {
    var id = ids.toString();
    var primaryKeyParams_1 = decodeId(id, primaryKey);
    if (isCompoundKey(primaryKey)) {
      if (resource.startsWith("rpc/")) {
        result = {};
        primaryKey.map(function(key, i) {
          return result[key] = "".concat(primaryKeyParams_1[i]);
        });
      } else {
        result = {
          and: "(".concat(primaryKey.map(function(key, i) {
            return "".concat(key, ".eq.").concat(primaryKeyParams_1[i]);
          }), ")")
        };
      }
    } else {
      result = (_b = {}, _b[primaryKey[0]] = "eq.".concat(id), _b);
    }
  }
  if (meta && meta.columns) {
    result.select = Array.isArray(meta.columns) ? meta.columns.join(",") : meta.columns;
  }
  return result;
};
var getOrderBy = function(field, order, primaryKey, sortOrder) {
  if (sortOrder === void 0) {
    sortOrder = "asc,desc";
  }
  var postgRestOrder = sortOrder.split(",")[order === "ASC" ? 0 : 1];
  if (field == "id") {
    return primaryKey.map(function(key) {
      return "".concat(key, ".").concat(postgRestOrder);
    }).join(",");
  } else {
    return "".concat(field, ".").concat(postgRestOrder);
  }
};

// node_modules/@raphiniert/ra-data-postgrest/esm/index.js
var import_qs = __toESM(require_lib());
var import_isEqual = __toESM(require_isEqual());
var __assign2 = function() {
  __assign2 = Object.assign || function(t) {
    for (var s, i = 1, n = arguments.length; i < n; i++) {
      s = arguments[i];
      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
        t[p] = s[p];
    }
    return t;
  };
  return __assign2.apply(this, arguments);
};
var defaultPrimaryKeys = /* @__PURE__ */ new Map();
var defaultSchema = function() {
  return "";
};
var useCustomSchema = function(schema, metaSchema, method) {
  var _a;
  var funcHeaderSchema = schema;
  if (metaSchema !== void 0) {
    funcHeaderSchema = function() {
      return metaSchema;
    };
  }
  if (funcHeaderSchema().length > 0) {
    var schemaHeader = "";
    if (["GET", "HEAD"].includes(method)) {
      schemaHeader = "Accept-Profile";
    } else if (["POST", "PATCH", "PUT", "DELETE"].includes(method)) {
      schemaHeader = "Content-Profile";
    } else
      return {};
    return _a = {}, _a[schemaHeader] = funcHeaderSchema(), _a;
  } else
    return {};
};
var esm_default = function(config) {
  return {
    getList: function(resource, params) {
      var _a, _b, _c, _d, _e, _f;
      if (params === void 0) {
        params = {};
      }
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var _g = params.pagination, page = _g.page, perPage = _g.perPage;
      var _h = params.sort || {}, field = _h.field, order = _h.order;
      var _j = parseFilters(params, config.defaultListOp), filter = _j.filter, select = _j.select;
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      var query = __assign2({ offset: String((page - 1) * perPage), limit: String(perPage) }, filter);
      if (field) {
        query.order = getOrderBy(field, order, primaryKey, config.sortOrder);
        if (((_b = params.meta) === null || _b === void 0 ? void 0 : _b.nullsfirst) && !query.order.includes("nullsfirst")) {
          query.order = query.order.includes("nullslast") ? query.order.replace("nullslast", "nullsfirst") : "".concat(query.order, ".nullsfirst");
        }
        if (((_c = params.meta) === null || _c === void 0 ? void 0 : _c.nullsfirst) === false && query.order.includes("nullsfirst")) {
          query.order = query.order.replace(".nullsfirst", "");
        }
        if (((_d = params.meta) === null || _d === void 0 ? void 0 : _d.nullslast) && !query.order.includes("nullslast")) {
          query.order = query.order.includes("nullsfirst") ? query.order.replace("nullsfirst", "nullslast") : "".concat(query.order, ".nullslast");
        }
        if (((_e = params.meta) === null || _e === void 0 ? void 0 : _e.nullslast) === false && query.order.includes("nullslast")) {
          query.order = query.order.replace(".nullslast", "");
        }
      }
      if (select) {
        query.select = select;
      }
      var options = {
        headers: new Headers(__assign2(__assign2({ Accept: "application/json", Prefer: "count=exact" }, ((_f = params.meta) === null || _f === void 0 ? void 0 : _f.headers) || {}), useCustomSchema(config.schema, metaSchema, "GET")))
      };
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      return config.httpClient(url, options).then(function(_a2) {
        var headers = _a2.headers, json = _a2.json;
        if (!headers.has("content-range")) {
          throw new Error("The Content-Range header is missing in the HTTP Response. The postgREST data provider expects\n          responses for lists of resources to contain this header with the total number of results to build\n          the pagination. If you are using CORS, did you declare Content-Range in the Access-Control-Expose-Headers header?");
        }
        return {
          data: json.map(function(obj) {
            return dataWithVirtualId(obj, primaryKey);
          }),
          total: parseInt(headers.get("content-range").split("/").pop(), 10)
        };
      });
    },
    getOne: function(resource, params) {
      var _a, _b;
      if (params === void 0) {
        params = {};
      }
      var id = params.id, meta = params.meta;
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var query = getQuery(primaryKey, id, resource, meta);
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      return config.httpClient(url, {
        headers: new Headers(__assign2(__assign2({ accept: "application/vnd.pgrst.object+json" }, ((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {}), useCustomSchema(config.schema, metaSchema, "GET")))
      }).then(function(_a2) {
        var json = _a2.json;
        return {
          data: dataWithVirtualId(json, primaryKey)
        };
      });
    },
    getMany: function(resource, params) {
      var _a;
      if (params === void 0) {
        params = {};
      }
      var ids = params.ids;
      if (ids.length === 0) {
        return Promise.resolve({ data: [] });
      }
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var query = getQuery(primaryKey, ids, resource, params.meta);
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      return config.httpClient(url, {
        headers: new Headers(__assign2({}, useCustomSchema(config.schema, metaSchema, "GET")))
      }).then(function(_a2) {
        var json = _a2.json;
        return {
          data: json.map(function(data) {
            return dataWithVirtualId(data, primaryKey);
          })
        };
      });
    },
    getManyReference: function(resource, params) {
      var _a;
      var _b, _c;
      if (params === void 0) {
        params = {};
      }
      var _d = params.pagination, page = _d.page, perPage = _d.perPage;
      var _e = params.sort, field = _e.field, order = _e.order;
      var _f = parseFilters(params, config.defaultListOp), filter = _f.filter, select = _f.select;
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var metaSchema = (_b = params.meta) === null || _b === void 0 ? void 0 : _b.schema;
      var query = params.target ? __assign2((_a = {}, _a[params.target] = "eq.".concat(params.id), _a.order = getOrderBy(field, order, primaryKey, config.sortOrder), _a.offset = String((page - 1) * perPage), _a.limit = String(perPage), _a), filter) : __assign2({ order: getOrderBy(field, order, primaryKey), offset: String((page - 1) * perPage), limit: String(perPage) }, filter);
      if (select) {
        query.select = select;
      }
      var options = {
        headers: new Headers(__assign2(__assign2({ Accept: "application/json", Prefer: "count=exact" }, ((_c = params.meta) === null || _c === void 0 ? void 0 : _c.headers) || {}), useCustomSchema(config.schema, metaSchema, "GET")))
      };
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      return config.httpClient(url, options).then(function(_a2) {
        var headers = _a2.headers, json = _a2.json;
        if (!headers.has("content-range")) {
          throw new Error("The Content-Range header is missing in the HTTP Response. The postgREST data provider expects\n          responses for lists of resources to contain this header with the total number of results to build\n          the pagination. If you are using CORS, did you declare Content-Range in the Access-Control-Expose-Headers header?");
        }
        return {
          data: json.map(function(data) {
            return dataWithVirtualId(data, primaryKey);
          }),
          total: parseInt(headers.get("content-range").split("/").pop(), 10)
        };
      });
    },
    update: function(resource, params) {
      var _a, _b;
      if (params === void 0) {
        params = {};
      }
      var id = params.id, data = params.data, meta = params.meta, previousData = params.previousData;
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var query = getQuery(primaryKey, id, resource, meta);
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      var changedData = getChanges(data, previousData);
      if (Object.keys(changedData).length === 0) {
        return Promise.resolve({ data: __assign2({}, previousData) });
      }
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      var body = JSON.stringify(__assign2({}, dataWithoutVirtualId(removePrimaryKey(changedData, primaryKey), primaryKey)));
      return config.httpClient(url, {
        method: "PATCH",
        headers: new Headers(__assign2(__assign2({ Accept: "application/vnd.pgrst.object+json", Prefer: "return=representation", "Content-Type": "application/json" }, ((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {}), useCustomSchema(config.schema, metaSchema, "PATCH"))),
        body
      }).then(function(_a2) {
        var json = _a2.json;
        return {
          data: dataWithVirtualId(json, primaryKey)
        };
      });
    },
    updateMany: function(resource, params) {
      var _a, _b;
      if (params === void 0) {
        params = {};
      }
      var ids = params.ids, meta = params.meta, data = params.data;
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var query = getQuery(primaryKey, ids, resource, meta);
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      var body = JSON.stringify(__assign2({}, dataWithoutVirtualId(removePrimaryKey(data, primaryKey), primaryKey)));
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      return config.httpClient(url, {
        method: "PATCH",
        headers: new Headers(__assign2(__assign2({ Prefer: "return=representation", "Content-Type": "application/json" }, ((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {}), useCustomSchema(config.schema, metaSchema, "PATCH"))),
        body
      }).then(function(_a2) {
        var json = _a2.json;
        return {
          data: json.map(function(data2) {
            return encodeId(data2, primaryKey);
          })
        };
      });
    },
    create: function(resource, params) {
      var _a, _b;
      if (params === void 0) {
        params = {};
      }
      var meta = params.meta;
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var query = getQuery(primaryKey, void 0, resource, meta);
      var queryStr = import_qs.default.stringify(query);
      var url = "".concat(config.apiUrl, "/").concat(resource).concat(queryStr.length > 0 ? "?" : "").concat(queryStr);
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      return config.httpClient(url, {
        method: "POST",
        headers: new Headers(__assign2(__assign2({ Accept: "application/vnd.pgrst.object+json", Prefer: "return=representation", "Content-Type": "application/json" }, ((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {}), useCustomSchema(config.schema, metaSchema, "POST"))),
        body: JSON.stringify(dataWithoutVirtualId(params.data, primaryKey))
      }).then(function(_a2) {
        var json = _a2.json;
        return {
          data: __assign2(__assign2({}, json), { id: encodeId(json, primaryKey) })
        };
      });
    },
    delete: function(resource, params) {
      var _a, _b;
      if (params === void 0) {
        params = {};
      }
      var id = params.id, meta = params.meta;
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var query = getQuery(primaryKey, id, resource, meta);
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      return config.httpClient(url, {
        method: "DELETE",
        headers: new Headers(__assign2(__assign2({ Accept: "application/vnd.pgrst.object+json", Prefer: "return=representation", "Content-Type": "application/json" }, ((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {}), useCustomSchema(config.schema, metaSchema, "DELETE")))
      }).then(function(_a2) {
        var json = _a2.json;
        return {
          data: dataWithVirtualId(json, primaryKey)
        };
      });
    },
    deleteMany: function(resource, params) {
      var _a, _b;
      if (params === void 0) {
        params = {};
      }
      var ids = params.ids, meta = params.meta;
      var primaryKey = getPrimaryKey(resource, config.primaryKeys);
      var query = getQuery(primaryKey, ids, resource, meta);
      var url = "".concat(config.apiUrl, "/").concat(resource, "?").concat(import_qs.default.stringify(query));
      var metaSchema = (_a = params.meta) === null || _a === void 0 ? void 0 : _a.schema;
      return config.httpClient(url, {
        method: "DELETE",
        headers: new Headers(__assign2(__assign2({ Prefer: "return=representation", "Content-Type": "application/json" }, ((_b = params.meta) === null || _b === void 0 ? void 0 : _b.headers) || {}), useCustomSchema(config.schema, metaSchema, "DELETE")))
      }).then(function(_a2) {
        var json = _a2.json;
        return {
          data: json.map(function(data) {
            return encodeId(data, primaryKey);
          })
        };
      });
    }
  };
};
var getChanges = function(data, previousData) {
  var changes = Object.keys(data).reduce(function(changes2, key) {
    if (!(0, import_isEqual.default)(data[key], previousData[key])) {
      changes2[key] = data[key];
    }
    return changes2;
  }, {});
  return changes;
};

// node_modules/ra-supabase-core/src/dataProvider.ts
var supabaseDataProvider = ({
  instanceUrl,
  apiKey,
  supabaseClient = createClient(instanceUrl, apiKey),
  httpClient = supabaseHttpClient({ apiKey, supabaseClient }),
  defaultListOp = "eq",
  primaryKeys = defaultPrimaryKeys,
  schema = defaultSchema,
  ...rest
}) => {
  const config = {
    apiUrl: `${instanceUrl}/rest/v1`,
    httpClient,
    defaultListOp,
    primaryKeys,
    schema,
    ...rest
  };
  return {
    supabaseClient: (url, options) => httpClient(`${config.apiUrl}/${url}`, options),
    getSchema: async () => {
      const { json } = await httpClient(`${config.apiUrl}/`, {});
      if (!json || !json.swagger) {
        throw new Error("The Open API schema is not readable");
      }
      return json;
    },
    ...esm_default(config)
  };
};
var supabaseHttpClient = ({
  apiKey,
  supabaseClient
}) => async (url, options = {}) => {
  const { data } = await supabaseClient.auth.getSession();
  if (!options.headers) options.headers = new Headers({});
  if (supabaseClient["headers"]) {
    Object.entries(supabaseClient["headers"]).forEach(
      ([name, value]) => options.headers.set(name, value)
    );
  }
  if (data.session) {
    options.user = {
      authenticated: true,
      // This ensures that users are identified correctly and that RLS can be applied
      token: `Bearer ${data.session.access_token}`
    };
  }
  options.headers.set("apiKey", apiKey);
  return fetch_exports.fetchJson(url, options);
};

// node_modules/ra-supabase-core/src/useAPISchema.ts
var useAPISchema = ({
  options
} = {}) => {
  const dataProvider = useDataProvider();
  if (!dataProvider.getSchema) {
    throw new Error(
      "The data provider doesn't have access to the database schema"
    );
  }
  return useQuery({
    queryKey: ["getSchema"],
    queryFn: () => dataProvider.getSchema(),
    staleTime: 1e3 * 60,
    // 1 minute
    ...options
  });
};

// node_modules/ra-supabase-core/src/useRedirectIfAuthenticated.ts
var import_react = __toESM(require_react());
var useRedirectIfAuthenticated = (redirectTo = "/") => {
  const navigate = useNavigate();
  const checkAuth = useCheckAuth();
  (0, import_react.useEffect)(() => {
    checkAuth({}, false, void 0).then(() => {
      navigate(redirectTo);
    }).catch(() => {
    });
  }, [checkAuth, navigate, redirectTo]);
};

// node_modules/ra-supabase-core/src/useResetPassword.ts
var useResetPassword = (options) => {
  const notify = useNotify();
  const redirect = useRedirect();
  const authProvider = useAuthProvider_default();
  if (authProvider == null) {
    throw new Error(
      "No authProvider found. Did you forget to set up an AuthProvider on the <Admin> component?"
    );
  }
  if (authProvider.resetPassword == null) {
    throw new Error(
      "The setPassword() method is missing from the AuthProvider although it is required. You may consider adding it"
    );
  }
  const {
    onSuccess = () => {
      redirect("/login");
      notify("ra-supabase.auth.password_reset", { type: "info" });
    },
    onError = (error) => notify(error.message, { type: "error" })
  } = options || {};
  const mutation = useMutation({
    mutationFn: (params) => {
      return authProvider.resetPassword(params);
    },
    onSuccess,
    onError,
    retry: false
  });
  return [mutation.mutate, mutation];
};

// node_modules/ra-supabase-core/src/useSetPassword.ts
var useSetPassword = (options) => {
  const notify = useNotify();
  const redirect = useRedirect();
  const authProvider = useAuthProvider_default();
  if (authProvider == null) {
    throw new Error(
      "No authProvider found. Did you forget to set up an AuthProvider on the <Admin> component?"
    );
  }
  if (authProvider.setPassword == null) {
    throw new Error(
      "The setPassword() method is missing from the AuthProvider although it is required. You may consider adding it"
    );
  }
  const {
    onSuccess = () => redirect("/"),
    onError = (error) => notify(error.message, { type: "error" })
  } = options || {};
  const mutation = useMutation({
    mutationFn: (params) => {
      return authProvider.setPassword(params);
    },
    onSuccess,
    onError,
    retry: false
  });
  return [mutation.mutate, mutation];
};

// node_modules/ra-supabase-core/src/useSupabaseAccessToken.ts
var import_react2 = __toESM(require_react());
var useSupabaseAccessToken = ({
  redirectTo = "/",
  parameterName = "access_token"
} = {}) => {
  const redirect = useRedirect();
  const searchStr = getSearchString();
  const urlSearchParams = new URLSearchParams(searchStr);
  const access_token = urlSearchParams.get(parameterName);
  (0, import_react2.useEffect)(() => {
    if (access_token == null) {
      if (redirectTo !== false) {
        redirect(redirectTo);
      }
    }
  });
  return access_token;
};

// node_modules/ra-supabase-ui-materialui/src/AuthLayout.tsx
var import_react3 = __toESM(require_react());
var import_Lock = __toESM(require_Lock());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var AuthLayout = (props) => {
  const {
    theme = defaultTheme,
    title,
    classes: classesOverride,
    className,
    children,
    notification = Notification,
    backgroundImage,
    ...rest
  } = props;
  const containerRef = (0, import_react3.useRef)(null);
  const muiTheme = (0, import_react3.useMemo)(() => createTheme_default(theme), [theme]);
  let backgroundImageLoaded = false;
  const updateBackgroundImage = () => {
    if (!backgroundImageLoaded && containerRef.current) {
      containerRef.current.style.backgroundImage = `url(${backgroundImage})`;
      backgroundImageLoaded = true;
    }
  };
  const lazyLoadBackgroundImage = () => {
    if (backgroundImage) {
      const img = new Image();
      img.onload = updateBackgroundImage;
      img.src = backgroundImage;
    }
  };
  (0, import_react3.useEffect)(() => {
    if (!backgroundImageLoaded) {
      lazyLoadBackgroundImage();
    }
  });
  return (0, import_jsx_runtime.jsx)(ThemeProvider, { theme: muiTheme, children: (0, import_jsx_runtime.jsxs)(Root, { ...rest, ref: containerRef, children: [
    (0, import_jsx_runtime.jsxs)(Card_default, { className: AuthLayoutClasses.card, children: [
      (0, import_jsx_runtime.jsx)("div", { className: AuthLayoutClasses.avatar, children: (0, import_jsx_runtime.jsx)(Avatar_default, { className: AuthLayoutClasses.icon, children: (0, import_jsx_runtime.jsx)(import_Lock.default, {}) }) }),
      children
    ] }),
    notification ? (0, import_react3.createElement)(notification) : null
  ] }) });
};
var PREFIX = "RaAuthLayout";
var AuthLayoutClasses = {
  card: `${PREFIX}-card`,
  avatar: `${PREFIX}-avatar`,
  icon: `${PREFIX}-icon`
};
var Root = styled_default("div", {
  name: PREFIX,
  overridesResolver: (props, styles) => styles.root
})(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  minHeight: "100vh",
  height: "1px",
  alignItems: "center",
  justifyContent: "flex-start",
  backgroundRepeat: "no-repeat",
  backgroundSize: "cover",
  backgroundImage: "radial-gradient(circle at 50% 14em, #313264 0%, #00023b 60%, #00023b 100%)",
  [`& .${AuthLayoutClasses.card}`]: {
    minWidth: 300,
    marginTop: "6em"
  },
  [`& .${AuthLayoutClasses.avatar}`]: {
    margin: "1em",
    display: "flex",
    justifyContent: "center"
  },
  [`& .${AuthLayoutClasses.icon}`]: {
    backgroundColor: theme.palette.grey[500]
  }
}));

// node_modules/ra-supabase-ui-materialui/src/guessers/CreateGuesser.tsx
var React2 = __toESM(require_react());
var import_inflection2 = __toESM(require_inflection());

// node_modules/ra-supabase-ui-materialui/src/guessers/inferElementFromType.ts
var import_inflection = __toESM(require_inflection());
var hasType = (type, types) => typeof types[type] !== "undefined";
var inferElementFromType = ({
  name,
  description,
  format,
  type,
  requiredFields,
  types,
  props
}) => {
  if (name === "id" && hasType("id", types)) {
    return new InferredElement_default(types.id, { source: "id" });
  }
  const validate = (requiredFields == null ? void 0 : requiredFields.includes(name)) ? [required()] : void 0;
  if ((description == null ? void 0 : description.startsWith("Note:\nThis is a Foreign Key to")) && hasType("reference", types)) {
    const reference = description.split("`")[1].split(".")[0];
    return new InferredElement_default(types.reference, {
      source: name,
      reference,
      ...props
    });
  }
  if (name.substring(name.length - 4) === "_ids" && hasType("reference", types)) {
    const reference = (0, import_inflection.pluralize)(name.substr(0, name.length - 4));
    return new InferredElement_default(types.referenceArray, {
      source: name,
      reference,
      ...props
    });
  }
  if (type === "array") {
    return new InferredElement_default(types.string, {
      source: name,
      validate
    });
  }
  if (type === "string") {
    if (name === "email" && hasType("email", types)) {
      return new InferredElement_default(types.email, {
        source: name,
        validate,
        ...props
      });
    }
    if (["url", "website"].includes(name) && hasType("url", types)) {
      return new InferredElement_default(types.url, {
        source: name,
        validate,
        ...props
      });
    }
    if (format && [
      "timestamp with time zone",
      "timestamp without time zone"
    ].includes(format) && hasType("date", types)) {
      return new InferredElement_default(types.date, {
        source: name,
        validate,
        ...props
      });
    }
  }
  if (type === "integer" && hasType("number", types)) {
    return new InferredElement_default(types.number, {
      source: name,
      validate,
      ...props
    });
  }
  if (type && hasType(type, types)) {
    return new InferredElement_default(types[type], {
      source: name,
      validate,
      ...props
    });
  }
  return new InferredElement_default(types.string, {
    source: name,
    validate,
    ...props
  });
};

// node_modules/ra-supabase-ui-materialui/src/guessers/CreateGuesser.tsx
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var CreateGuesser = (props) => {
  const {
    mutationOptions,
    resource,
    record,
    transform,
    redirect,
    disableAuthentication,
    ...rest
  } = props;
  return (0, import_jsx_runtime2.jsx)(
    CreateBase,
    {
      resource,
      record,
      redirect,
      transform,
      mutationOptions,
      disableAuthentication,
      children: (0, import_jsx_runtime2.jsx)(CreateGuesserView, { ...rest })
    }
  );
};
var CreateGuesserView = (props) => {
  const { data: schema, error, isPending } = useAPISchema();
  const resource = useResourceContext();
  const [child, setChild] = React2.useState(null);
  if (!resource) {
    throw new Error("CreateGuesser must be used withing a ResourceContext");
  }
  const { enableLog = true, ...rest } = props;
  React2.useEffect(() => {
    var _a;
    if (isPending || error) {
      return;
    }
    const resourceDefinition = (_a = schema.definitions) == null ? void 0 : _a[resource];
    const requiredFields = (resourceDefinition == null ? void 0 : resourceDefinition.required) || [];
    if (!resourceDefinition || !resourceDefinition.properties) {
      throw new Error(
        `The resource ${resource} is not defined in the API schema`
      );
    }
    const inferredInputs = Object.keys(resourceDefinition.properties).filter((source) => source !== "id").filter(
      (source) => resourceDefinition.properties[source].format !== "tsvector"
    ).map(
      (source) => inferElementFromType({
        name: source,
        types: editFieldTypes,
        description: resourceDefinition.properties[source].description,
        format: resourceDefinition.properties[source].format,
        type: resourceDefinition.properties && resourceDefinition.properties[source] && typeof resourceDefinition.properties[source].type === "string" ? resourceDefinition.properties[source].type : "string",
        requiredFields
      })
    );
    const inferredForm = new InferredElement_default(
      editFieldTypes.form,
      null,
      inferredInputs
    );
    setChild(inferredForm.getElement());
    if (!enableLog) return;
    const representation = inferredForm.getRepresentation();
    const components = ["Create"].concat(
      Array.from(
        new Set(
          Array.from(representation.matchAll(/<([^/\s>]+)/g)).map((match) => match[1]).filter((component) => component !== "span")
        )
      )
    ).sort();
    console.log(
      `Guessed Create:
            
import { ${components.join(", ")} } from 'react-admin';
            
export const ${(0, import_inflection2.capitalize)((0, import_inflection2.singularize)(resource))}Create = () => (
    <Create>
${representation}
    </Create>
);`
    );
  }, [resource, isPending, error, schema, enableLog]);
  if (isPending) return (0, import_jsx_runtime2.jsx)(Loading, {});
  if (error) return (0, import_jsx_runtime2.jsxs)("p", { children: [
    "Error: ",
    error.message
  ] });
  return (0, import_jsx_runtime2.jsx)(CreateView, { ...rest, children: child });
};

// node_modules/ra-supabase-ui-materialui/src/guessers/EditGuesser.tsx
var React3 = __toESM(require_react());
var import_inflection3 = __toESM(require_inflection());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var EditGuesser = (props) => {
  const {
    resource,
    id,
    mutationMode,
    mutationOptions,
    queryOptions,
    redirect,
    transform,
    disableAuthentication,
    ...rest
  } = props;
  return (0, import_jsx_runtime3.jsx)(
    EditBase,
    {
      resource,
      id,
      mutationMode,
      mutationOptions,
      queryOptions,
      redirect,
      transform,
      disableAuthentication,
      children: (0, import_jsx_runtime3.jsx)(EditGuesserView, { ...rest })
    }
  );
};
var EditGuesserView = (props) => {
  const { data: schema, error, isPending } = useAPISchema();
  const resource = useResourceContext();
  const [child, setChild] = React3.useState(null);
  if (!resource) {
    throw new Error("EditGuesser must be used withing a ResourceContext");
  }
  const { enableLog = true, ...rest } = props;
  React3.useEffect(() => {
    var _a;
    if (isPending || error) {
      return;
    }
    const resourceDefinition = (_a = schema.definitions) == null ? void 0 : _a[resource];
    const requiredFields = (resourceDefinition == null ? void 0 : resourceDefinition.required) || [];
    if (!resourceDefinition || !resourceDefinition.properties) {
      throw new Error(
        `The resource ${resource} is not defined in the API schema`
      );
    }
    const inferredInputs = Object.keys(resourceDefinition.properties).filter((source) => source !== "id").filter(
      (source) => resourceDefinition.properties[source].format !== "tsvector"
    ).map(
      (source) => inferElementFromType({
        name: source,
        types: editFieldTypes,
        description: resourceDefinition.properties[source].description,
        format: resourceDefinition.properties[source].format,
        type: resourceDefinition.properties && resourceDefinition.properties[source] && typeof resourceDefinition.properties[source].type === "string" ? resourceDefinition.properties[source].type : "string",
        requiredFields
      })
    );
    const inferredForm = new InferredElement_default(
      editFieldTypes.form,
      null,
      inferredInputs
    );
    setChild(inferredForm.getElement());
    if (!enableLog) return;
    const representation = inferredForm.getRepresentation();
    const components = ["Edit"].concat(
      Array.from(
        new Set(
          Array.from(representation.matchAll(/<([^/\s>]+)/g)).map((match) => match[1]).filter((component) => component !== "span")
        )
      )
    ).sort();
    console.log(
      `Guessed Edit:
            
import { ${components.join(", ")} } from 'react-admin';
            
export const ${(0, import_inflection3.capitalize)((0, import_inflection3.singularize)(resource))}Edit = () => (
    <Edit>
${representation}
    </Edit>
);`
    );
  }, [resource, isPending, error, schema, enableLog]);
  if (isPending) return (0, import_jsx_runtime3.jsx)(Loading, {});
  if (error) return (0, import_jsx_runtime3.jsxs)("p", { children: [
    "Error: ",
    error.message
  ] });
  return (0, import_jsx_runtime3.jsx)(EditView, { ...rest, children: child });
};

// node_modules/ra-supabase-ui-materialui/src/guessers/ListGuesser.tsx
var React4 = __toESM(require_react());
var import_inflection4 = __toESM(require_inflection());
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var ListGuesser = (props) => {
  const {
    debounce,
    disableAuthentication,
    disableSyncWithLocation,
    exporter,
    filter,
    filterDefaultValues,
    perPage,
    queryOptions,
    resource,
    sort,
    storeKey,
    ...rest
  } = props;
  return (0, import_jsx_runtime4.jsx)(
    ListBase,
    {
      debounce,
      disableAuthentication,
      disableSyncWithLocation,
      exporter,
      filter,
      filterDefaultValues,
      perPage,
      queryOptions,
      resource,
      sort,
      storeKey,
      children: (0, import_jsx_runtime4.jsx)(ListGuesserView, { ...rest })
    }
  );
};
var ListGuesserView = (props) => {
  const { data: schema, error, isPending } = useAPISchema();
  const resource = useResourceContext();
  const [child, setChild] = React4.useState(null);
  const [filters, setFilters] = React4.useState(void 0);
  if (!resource) {
    throw new Error("ListGuesser must be used withing a ResourceContext");
  }
  const { enableLog = true, ...rest } = props;
  React4.useEffect(() => {
    var _a, _b;
    if (isPending || error) {
      return;
    }
    const resourceDefinition = (_a = schema.definitions) == null ? void 0 : _a[resource];
    if (!resourceDefinition || !resourceDefinition.properties) {
      throw new Error(
        `The resource ${resource} is not defined in the API schema`
      );
    }
    const inferredFields = Object.keys(resourceDefinition.properties).filter(
      (source) => resourceDefinition.properties[source].format !== "tsvector"
    ).map(
      (source) => inferElementFromType({
        name: source,
        types: listFieldTypes,
        description: resourceDefinition.properties[source].description,
        format: resourceDefinition.properties[source].format,
        type: resourceDefinition.properties && resourceDefinition.properties[source] && typeof resourceDefinition.properties[source].type === "string" ? resourceDefinition.properties[source].type : "string"
      })
    );
    const inferredTable = new InferredElement_default(
      listFieldTypes.table,
      null,
      inferredFields
    );
    setChild(inferredTable.getElement());
    const rowFilters = ((_b = schema.paths[`/${resource}`].get.parameters) == null ? void 0 : _b.filter(
      (obj) => obj["$ref"].includes("rowFilter")
    ).map((obj) => obj["$ref"].split(".").pop())) ?? [];
    const inferredInputsForFilters = rowFilters.filter(
      (source) => resourceDefinition.properties[source].format !== "tsvector"
    ).map((source) => {
      const field = resourceDefinition.properties[source];
      return inferElementFromType({
        name: source,
        types: editFieldTypes,
        description: field.description,
        format: field.format,
        type: field.type
      });
    });
    if (rowFilters.some(
      (source) => resourceDefinition.properties[source].format === "tsvector"
    )) {
      const fullTextSearchSource = rowFilters.find(
        (source) => resourceDefinition.properties[source].format === "tsvector"
      );
      const field = resourceDefinition.properties[fullTextSearchSource];
      inferredInputsForFilters.unshift(
        inferElementFromType({
          name: `${fullTextSearchSource}@fts`,
          types: {
            string: {
              component: SearchInput,
              representation: (props2) => `<SearchInput alwaysOn source="${props2.source}" />`
            }
          },
          description: field.description,
          format: "tsvector",
          props: {
            alwaysOn: true,
            parse: (value) => value ? `${value}:*` : void 0,
            format: (value) => value ? value.substring(0, value.length - 2) : ""
          },
          type: field.type
        })
      );
    }
    if (inferredInputsForFilters.length > 0) {
      const filterElements = inferredInputsForFilters.map((inferredInput) => inferredInput.getElement()).filter((el) => el != null);
      setFilters(filterElements);
    }
    if (!enableLog) return;
    const tableRepresentation = inferredTable.getRepresentation();
    const filterRepresentation = inferredInputsForFilters.length > 0 ? `const filters = [
${inferredInputsForFilters.map((inferredInput) => "    " + inferredInput.getRepresentation()).join(",\n")}
];
` : "";
    const fieldComponents = Array.from(
      tableRepresentation.matchAll(/<([^/\s>]+)/g)
    ).map((match) => match[1]).filter((component) => component !== "span");
    const filterComponents = Array.from(
      filterRepresentation.matchAll(/<([^/\s>]+)/g)
    ).map((match) => match[1]).filter((component) => component !== "span");
    const components = Array.from(
      /* @__PURE__ */ new Set(["List", ...fieldComponents, ...filterComponents])
    ).sort();
    console.log(
      `Guessed List:
            
import { ${components.join(", ")} } from 'react-admin';

${filterRepresentation}
export const ${(0, import_inflection4.capitalize)((0, import_inflection4.singularize)(resource))}List = () => (
    <List${filterRepresentation ? " filters={filters}" : ""}>
${tableRepresentation}
    </List>
);`
    );
  }, [resource, isPending, error, schema, enableLog]);
  if (isPending) return (0, import_jsx_runtime4.jsx)(Loading, {});
  if (error) return (0, import_jsx_runtime4.jsxs)("p", { children: [
    "Error: ",
    error.message
  ] });
  return (0, import_jsx_runtime4.jsx)(ListView, { filters, ...rest, children: child });
};

// node_modules/ra-supabase-ui-materialui/src/guessers/useCrudGuesser.tsx
var import_react4 = __toESM(require_react());

// node_modules/ra-supabase-ui-materialui/src/guessers/ShowGuesser.tsx
var React5 = __toESM(require_react());
var import_inflection5 = __toESM(require_inflection());
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var ShowGuesser = (props) => {
  const { id, disableAuthentication, queryOptions, resource, ...rest } = props;
  return (0, import_jsx_runtime5.jsx)(
    ShowBase,
    {
      id,
      disableAuthentication,
      queryOptions,
      resource,
      children: (0, import_jsx_runtime5.jsx)(ShowGuesserView, { ...rest })
    }
  );
};
var ShowGuesserView = (props) => {
  const { data: schema, error, isPending } = useAPISchema();
  const resource = useResourceContext();
  const [child, setChild] = React5.useState(null);
  if (!resource) {
    throw new Error("ShowGuesser must be used withing a ResourceContext");
  }
  const { enableLog = true, ...rest } = props;
  React5.useEffect(() => {
    var _a;
    if (isPending || error) {
      return;
    }
    const resourceDefinition = (_a = schema.definitions) == null ? void 0 : _a[resource];
    if (!resourceDefinition || !resourceDefinition.properties) {
      throw new Error(
        `The resource ${resource} is not defined in the API schema`
      );
    }
    const inferredFields = Object.keys(resourceDefinition.properties).filter(
      (source) => resourceDefinition.properties[source].format !== "tsvector"
    ).map(
      (source) => inferElementFromType({
        name: source,
        types: showFieldTypes,
        description: resourceDefinition.properties[source].description,
        format: resourceDefinition.properties[source].format,
        type: resourceDefinition.properties && resourceDefinition.properties[source] && typeof resourceDefinition.properties[source].type === "string" ? resourceDefinition.properties[source].type : "string"
      })
    );
    const inferredLayout = new InferredElement_default(
      showFieldTypes.show,
      null,
      inferredFields
    );
    setChild(inferredLayout.getElement());
    if (!enableLog) return;
    const representation = inferredLayout.getRepresentation();
    const components = ["Show"].concat(
      Array.from(
        new Set(
          Array.from(representation.matchAll(/<([^/\s>]+)/g)).map((match) => match[1]).filter((component) => component !== "span")
        )
      )
    ).sort();
    console.log(
      `Guessed Show:
            
import { ${components.join(", ")} } from 'react-admin';
            
export const ${(0, import_inflection5.capitalize)((0, import_inflection5.singularize)(resource))}Show = () => (
    <Show>
${representation}
    </Show>
);`
    );
  }, [resource, isPending, error, schema, enableLog]);
  if (isPending) return (0, import_jsx_runtime5.jsx)(Loading, {});
  if (error) return (0, import_jsx_runtime5.jsxs)("p", { children: [
    "Error: ",
    error.message
  ] });
  return (0, import_jsx_runtime5.jsx)(ShowView, { ...rest, children: child });
};

// node_modules/ra-supabase-ui-materialui/src/guessers/useCrudGuesser.tsx
var useCrudGuesser = () => {
  const { data: schema, error, isPending } = useAPISchema();
  return (0, import_react4.useMemo)(() => {
    if (isPending || error) {
      return [];
    }
    const resourceNames = Object.keys(schema.definitions);
    return resourceNames.map((name) => {
      const resourcePaths = schema.paths[`/${name}`] ?? {};
      return {
        name,
        list: resourcePaths.get ? ListGuesser : void 0,
        show: resourcePaths.get ? ShowGuesser : void 0,
        edit: resourcePaths.patch ? EditGuesser : void 0,
        create: resourcePaths.post ? CreateGuesser : void 0
      };
    });
  }, [schema, isPending, error]);
};

// node_modules/ra-supabase-ui-materialui/src/ForgotPasswordForm.tsx
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var ForgotPasswordForm = () => {
  const notify = useNotify();
  const translate = useTranslate();
  const [resetPassword] = useResetPassword({
    onError: (error) => {
      notify(
        typeof error === "string" ? error : typeof error === "undefined" || !error.message ? "ra.auth.sign_in_error" : error.message,
        {
          type: "warning",
          messageArgs: {
            _: typeof error === "string" ? error : error && error.message ? error.message : void 0
          }
        }
      );
    }
  });
  const submit = (values) => {
    return resetPassword({
      email: values.email
    });
  };
  return (0, import_jsx_runtime6.jsxs)(Root2, { onSubmit: submit, children: [
    (0, import_jsx_runtime6.jsxs)("div", { className: SupabaseLoginFormClasses.container, children: [
      (0, import_jsx_runtime6.jsxs)(Stack_default, { spacing: 1, children: [
        (0, import_jsx_runtime6.jsx)(Typography_default, { variant: "h5", textAlign: "center", children: translate(
          "ra-supabase.reset_password.forgot_password",
          { _: "Forgot password?" }
        ) }),
        (0, import_jsx_runtime6.jsx)(
          Typography_default,
          {
            variant: "body2",
            color: "textSecondary",
            textAlign: "center",
            children: translate(
              "ra-supabase.reset_password.forgot_password_details",
              {
                _: "Enter your email to receive a reset password link."
              }
            )
          }
        )
      ] }),
      (0, import_jsx_runtime6.jsx)("div", { className: SupabaseLoginFormClasses.input, children: (0, import_jsx_runtime6.jsx)(
        TextInput,
        {
          source: "email",
          label: translate("ra.auth.email", {
            _: "Email"
          }),
          autoComplete: "email",
          fullWidth: true,
          validate: required()
        }
      ) })
    ] }),
    (0, import_jsx_runtime6.jsxs)(CardActions_default, { sx: { flexDirection: "column", gap: 1 }, children: [
      (0, import_jsx_runtime6.jsx)(
        SaveButton,
        {
          variant: "contained",
          type: "submit",
          className: SupabaseLoginFormClasses.button,
          label: translate("ra.action.reset_password", {
            _: "Reset password"
          }),
          icon: (0, import_jsx_runtime6.jsx)(import_jsx_runtime6.Fragment, {})
        }
      ),
      (0, import_jsx_runtime6.jsx)(Link, { to: "/login", variant: "body2", children: translate("ra-supabase.auth.back_to_login", {
        _: "Back to login page"
      }) })
    ] })
  ] });
};
var PREFIX2 = "RaSupabaseForgotPasswordForm";
var SupabaseLoginFormClasses = {
  container: `${PREFIX2}-container`,
  input: `${PREFIX2}-input`,
  button: `${PREFIX2}-button`
};
var Root2 = styled_default(Form, {
  name: PREFIX2,
  overridesResolver: (props, styles) => styles.root
})(({ theme }) => ({
  [`& .${SupabaseLoginFormClasses.container}`]: {
    padding: "0 1em 0 1em"
  },
  [`& .${SupabaseLoginFormClasses.input}`]: {
    marginTop: "1em"
  },
  [`& .${SupabaseLoginFormClasses.button}`]: {
    width: "100%"
  }
}));

// node_modules/ra-supabase-ui-materialui/src/ForgotPasswordPage.tsx
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var ForgotPasswordPage = (props) => {
  const { children = (0, import_jsx_runtime7.jsx)(ForgotPasswordForm, {}) } = props;
  return (0, import_jsx_runtime7.jsx)(AuthLayout, { children });
};
ForgotPasswordPage.path = "/forgot-password";

// node_modules/ra-supabase-ui-materialui/src/LoginForm.tsx
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var LoginForm = ({
  disableForgotPassword,
  ...props
}) => {
  const login = useLogin_default();
  const notify = useNotify();
  const translate = useTranslate();
  const submit = (values) => {
    return login(values).catch((error) => {
      notify(
        typeof error === "string" ? error : typeof error === "undefined" || !error.message ? "ra.auth.sign_in_error" : error.message,
        {
          type: "warning",
          messageArgs: {
            _: typeof error === "string" ? error : error && error.message ? error.message : void 0
          }
        }
      );
    });
  };
  return (0, import_jsx_runtime8.jsxs)(Root3, { onSubmit: submit, ...props, children: [
    (0, import_jsx_runtime8.jsxs)("div", { className: SupabaseLoginFormClasses2.container, children: [
      (0, import_jsx_runtime8.jsx)("div", { className: SupabaseLoginFormClasses2.input, children: (0, import_jsx_runtime8.jsx)(
        TextInput,
        {
          autoFocus: true,
          source: "email",
          type: "email",
          label: translate("ra-supabase.auth.email", {
            _: "Email"
          }),
          fullWidth: true,
          validate: required()
        }
      ) }),
      (0, import_jsx_runtime8.jsx)("div", { children: (0, import_jsx_runtime8.jsx)(
        PasswordInput,
        {
          source: "password",
          label: translate("ra.auth.password", {
            _: "Password"
          }),
          autoComplete: "current-password",
          fullWidth: true,
          validate: required()
        }
      ) })
    ] }),
    (0, import_jsx_runtime8.jsxs)(CardActions_default, { sx: { flexDirection: "column", gap: 1 }, children: [
      (0, import_jsx_runtime8.jsx)(
        SaveButton,
        {
          variant: "contained",
          type: "submit",
          className: SupabaseLoginFormClasses2.button,
          label: translate("ra.auth.sign_in"),
          icon: (0, import_jsx_runtime8.jsx)(import_jsx_runtime8.Fragment, {})
        }
      ),
      !disableForgotPassword ? (0, import_jsx_runtime8.jsx)(Link, { to: ForgotPasswordPage.path, variant: "body2", children: translate("ra-supabase.auth.forgot_password", {
        _: "Forgot password?"
      }) }) : null
    ] })
  ] });
};
var PREFIX3 = "RaSupabaseLoginForm";
var SupabaseLoginFormClasses2 = {
  container: `${PREFIX3}-container`,
  input: `${PREFIX3}-input`,
  button: `${PREFIX3}-button`
};
var Root3 = styled_default(Form, {
  name: PREFIX3,
  overridesResolver: (props, styles) => styles.root
})(({ theme }) => ({
  [`& .${SupabaseLoginFormClasses2.container}`]: {
    padding: "0 1em 1em 1em"
  },
  [`& .${SupabaseLoginFormClasses2.input}`]: {
    marginTop: "1em"
  },
  [`& .${SupabaseLoginFormClasses2.button}`]: {
    width: "100%"
  }
}));

// node_modules/ra-supabase-ui-materialui/src/icons.tsx
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var GoogleIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: [
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#FFC107",
          d: "M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#FF3D00",
          d: "M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#4CAF50",
          d: "M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#1976D2",
          d: "M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
        }
      )
    ]
  }
);
var FacebookIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: [
      (0, import_jsx_runtime9.jsx)("path", { fill: "#039be5", d: "M24 5A19 19 0 1 0 24 43A19 19 0 1 0 24 5Z" }),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#fff",
          d: "M26.572,29.036h4.917l0.772-4.995h-5.69v-2.73c0-2.075,0.678-3.915,2.619-3.915h3.119v-4.359c-0.548-0.074-1.707-0.236-3.897-0.236c-4.573,0-7.254,2.415-7.254,7.917v3.323h-4.701v4.995h4.701v13.729C22.089,42.905,23.032,43,24,43c0.875,0,1.729-0.08,2.572-0.194V29.036z"
        }
      )
    ]
  }
);
var TwitterIcon = (props) => (0, import_jsx_runtime9.jsx)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: (0, import_jsx_runtime9.jsx)(
      "path",
      {
        fill: "#03A9F4",
        d: "M42,12.429c-1.323,0.586-2.746,0.977-4.247,1.162c1.526-0.906,2.7-2.351,3.251-4.058c-1.428,0.837-3.01,1.452-4.693,1.776C34.967,9.884,33.05,9,30.926,9c-4.08,0-7.387,3.278-7.387,7.32c0,0.572,0.067,1.129,0.193,1.67c-6.138-0.308-11.582-3.226-15.224-7.654c-0.64,1.082-1,2.349-1,3.686c0,2.541,1.301,4.778,3.285,6.096c-1.211-0.037-2.351-0.374-3.349-0.914c0,0.022,0,0.055,0,0.086c0,3.551,2.547,6.508,5.923,7.181c-0.617,0.169-1.269,0.263-1.941,0.263c-0.477,0-0.942-0.054-1.392-0.135c0.94,2.902,3.667,5.023,6.898,5.086c-2.528,1.96-5.712,3.134-9.174,3.134c-0.598,0-1.183-0.034-1.761-0.104C9.268,36.786,13.152,38,17.321,38c13.585,0,21.017-11.156,21.017-20.834c0-0.317-0.01-0.633-0.025-0.945C39.763,15.197,41.013,13.905,42,12.429"
      }
    )
  }
);
var AppleIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    fill: "gray",
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    width: "512px",
    height: "512px",
    children: [
      " ",
      (0, import_jsx_runtime9.jsx)("path", { d: "M 15.904297 1.078125 C 15.843359 1.06875 15.774219 1.0746094 15.699219 1.0996094 C 14.699219 1.2996094 13.600391 1.8996094 12.900391 2.5996094 C 12.300391 3.1996094 11.800781 4.1996094 11.800781 5.0996094 C 11.800781 5.2996094 11.999219 5.5 12.199219 5.5 C 13.299219 5.4 14.399609 4.7996094 15.099609 4.0996094 C 15.699609 3.2996094 16.199219 2.4 16.199219 1.5 C 16.199219 1.275 16.087109 1.10625 15.904297 1.078125 z M 16.199219 5.4003906 C 14.399219 5.4003906 13.600391 6.5 12.400391 6.5 C 11.100391 6.5 9.9003906 5.5 8.4003906 5.5 C 6.3003906 5.5 3.0996094 7.4996094 3.0996094 12.099609 C 2.9996094 16.299609 6.8 21 9 21 C 10.3 21 10.600391 20.199219 12.400391 20.199219 C 14.200391 20.199219 14.600391 21 15.900391 21 C 17.400391 21 18.500391 19.399609 19.400391 18.099609 C 19.800391 17.399609 20.100391 17.000391 20.400391 16.400391 C 20.600391 16.000391 20.4 15.600391 20 15.400391 C 17.4 14.100391 16.900781 9.9003906 19.800781 8.4003906 C 20.300781 8.1003906 20.4 7.4992188 20 7.1992188 C 18.9 6.1992187 17.299219 5.4003906 16.199219 5.4003906 z" })
    ]
  }
);
var GithubIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    fill: "gray",
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 30 30",
    width: "512px",
    height: "512px",
    children: [
      " ",
      (0, import_jsx_runtime9.jsx)("path", { d: "M15,3C8.373,3,3,8.373,3,15c0,5.623,3.872,10.328,9.092,11.63C12.036,26.468,12,26.28,12,26.047v-2.051 c-0.487,0-1.303,0-1.508,0c-0.821,0-1.551-0.353-1.905-1.009c-0.393-0.729-0.461-1.844-1.435-2.526 c-0.289-0.227-0.069-0.486,0.264-0.451c0.615,0.174,1.125,0.596,1.605,1.222c0.478,0.627,0.703,0.769,1.596,0.769 c0.433,0,1.081-0.025,1.691-0.121c0.328-0.833,0.895-1.6,1.588-1.962c-3.996-0.411-5.903-2.399-5.903-5.098 c0-1.162,0.495-2.286,1.336-3.233C9.053,10.647,8.706,8.73,9.435,8c1.798,0,2.885,1.166,3.146,1.481C13.477,9.174,14.461,9,15.495,9 c1.036,0,2.024,0.174,2.922,0.483C18.675,9.17,19.763,8,21.565,8c0.732,0.731,0.381,2.656,0.102,3.594 c0.836,0.945,1.328,2.066,1.328,3.226c0,2.697-1.904,4.684-5.894,5.097C18.199,20.49,19,22.1,19,23.313v2.734 c0,0.104-0.023,0.179-0.035,0.268C23.641,24.676,27,20.236,27,15C27,8.373,21.627,3,15,3z" })
    ]
  }
);
var GitlabIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: [
      (0, import_jsx_runtime9.jsx)("path", { fill: "#e53935", d: "M24 43L16 20 32 20z" }),
      (0, import_jsx_runtime9.jsx)("path", { fill: "#ff7043", d: "M24 43L42 20 32 20z" }),
      (0, import_jsx_runtime9.jsx)("path", { fill: "#e53935", d: "M37 5L42 20 32 20z" }),
      (0, import_jsx_runtime9.jsx)("path", { fill: "#ffa726", d: "M24 43L42 20 45 28z" }),
      (0, import_jsx_runtime9.jsx)("path", { fill: "#ff7043", d: "M24 43L6 20 16 20z" }),
      (0, import_jsx_runtime9.jsx)("path", { fill: "#e53935", d: "M11 5L6 20 16 20z" }),
      (0, import_jsx_runtime9.jsx)("path", { fill: "#ffa726", d: "M24 43L6 20 3 28z" })
    ]
  }
);
var BitbucketIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    width: "512",
    height: "512",
    viewBox: "0 0 62.42 62.42",
    children: [
      (0, import_jsx_runtime9.jsx)("defs", { children: (0, import_jsx_runtime9.jsxs)(
        "linearGradient",
        {
          id: "New_Gradient_Swatch_1",
          x1: "64.01",
          y1: "30.27",
          x2: "32.99",
          y2: "54.48",
          gradientUnits: "userSpaceOnUse",
          children: [
            (0, import_jsx_runtime9.jsx)("stop", { offset: "0.18", stopColor: "#0052cc" }),
            (0, import_jsx_runtime9.jsx)("stop", { offset: "1", stopColor: "#2684ff" })
          ]
        }
      ) }),
      (0, import_jsx_runtime9.jsx)("title", { children: "Bitbucket-blue" }),
      (0, import_jsx_runtime9.jsx)("g", { id: "Layer_2", "data-name": "Layer 2", children: (0, import_jsx_runtime9.jsxs)("g", { id: "Blue", transform: "translate(0 -3.13)", children: [
        (0, import_jsx_runtime9.jsx)(
          "path",
          {
            d: "M2,6.26A2,2,0,0,0,0,8.58L8.49,60.12a2.72,2.72,0,0,0,2.66,2.27H51.88a2,2,0,0,0,2-1.68L62.37,8.59a2,2,0,0,0-2-2.32ZM37.75,43.51h-13L21.23,25.12H40.9Z",
            fill: "#2684ff"
          }
        ),
        (0, import_jsx_runtime9.jsx)(
          "path",
          {
            d: "M59.67,25.12H40.9L37.75,43.51h-13L9.4,61.73a2.71,2.71,0,0,0,1.75.66H51.89a2,2,0,0,0,2-1.68Z",
            fill: "url(#New_Gradient_Swatch_1)"
          }
        )
      ] }) })
    ]
  }
);
var DiscordIcon = (props) => (0, import_jsx_runtime9.jsx)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: (0, import_jsx_runtime9.jsx)(
      "path",
      {
        fill: "#536dfe",
        d: "M39.248,10.177c-2.804-1.287-5.812-2.235-8.956-2.778c-0.057-0.01-0.114,0.016-0.144,0.068	c-0.387,0.688-0.815,1.585-1.115,2.291c-3.382-0.506-6.747-0.506-10.059,0c-0.3-0.721-0.744-1.603-1.133-2.291	c-0.03-0.051-0.087-0.077-0.144-0.068c-3.143,0.541-6.15,1.489-8.956,2.778c-0.024,0.01-0.045,0.028-0.059,0.051	c-5.704,8.522-7.267,16.835-6.5,25.044c0.003,0.04,0.026,0.079,0.057,0.103c3.763,2.764,7.409,4.442,10.987,5.554	c0.057,0.017,0.118-0.003,0.154-0.051c0.846-1.156,1.601-2.374,2.248-3.656c0.038-0.075,0.002-0.164-0.076-0.194	c-1.197-0.454-2.336-1.007-3.432-1.636c-0.087-0.051-0.094-0.175-0.014-0.234c0.231-0.173,0.461-0.353,0.682-0.534	c0.04-0.033,0.095-0.04,0.142-0.019c7.201,3.288,14.997,3.288,22.113,0c0.047-0.023,0.102-0.016,0.144,0.017	c0.22,0.182,0.451,0.363,0.683,0.536c0.08,0.059,0.075,0.183-0.012,0.234c-1.096,0.641-2.236,1.182-3.434,1.634	c-0.078,0.03-0.113,0.12-0.075,0.196c0.661,1.28,1.415,2.498,2.246,3.654c0.035,0.049,0.097,0.07,0.154,0.052	c3.595-1.112,7.241-2.79,11.004-5.554c0.033-0.024,0.054-0.061,0.057-0.101c0.917-9.491-1.537-17.735-6.505-25.044	C39.293,10.205,39.272,10.187,39.248,10.177z M16.703,30.273c-2.168,0-3.954-1.99-3.954-4.435s1.752-4.435,3.954-4.435	c2.22,0,3.989,2.008,3.954,4.435C20.658,28.282,18.906,30.273,16.703,30.273z M31.324,30.273c-2.168,0-3.954-1.99-3.954-4.435	s1.752-4.435,3.954-4.435c2.22,0,3.989,2.008,3.954,4.435C35.278,28.282,33.544,30.273,31.324,30.273z"
      }
    )
  }
);
var AzureIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: [
      (0, import_jsx_runtime9.jsxs)(
        "linearGradient",
        {
          id: "k8yl7~hDat~FaoWq8WjN6a",
          x1: "-1254.397",
          x2: "-1261.911",
          y1: "877.268",
          y2: "899.466",
          gradientTransform: "translate(1981.75 -1362.063) scale(1.5625)",
          gradientUnits: "userSpaceOnUse",
          children: [
            (0, import_jsx_runtime9.jsx)("stop", { offset: "0", stopColor: "#114a8b" }),
            (0, import_jsx_runtime9.jsx)("stop", { offset: "1", stopColor: "#0669bc" })
          ]
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "url(#k8yl7~hDat~FaoWq8WjN6a)",
          d: "M17.634,6h11.305L17.203,40.773c-0.247,0.733-0.934,1.226-1.708,1.226H6.697 c-0.994,0-1.8-0.806-1.8-1.8c0-0.196,0.032-0.39,0.094-0.576L15.926,7.227C16.173,6.494,16.86,6,17.634,6L17.634,6z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#0078d4",
          d: "M34.062,29.324H16.135c-0.458-0.001-0.83,0.371-0.831,0.829c0,0.231,0.095,0.451,0.264,0.608 l11.52,10.752C27.423,41.826,27.865,42,28.324,42h10.151L34.062,29.324z"
        }
      ),
      (0, import_jsx_runtime9.jsxs)(
        "linearGradient",
        {
          id: "k8yl7~hDat~FaoWq8WjN6b",
          x1: "-1252.05",
          x2: "-1253.788",
          y1: "887.612",
          y2: "888.2",
          gradientTransform: "translate(1981.75 -1362.063) scale(1.5625)",
          gradientUnits: "userSpaceOnUse",
          children: [
            (0, import_jsx_runtime9.jsx)("stop", { offset: "0", stopOpacity: ".3" }),
            (0, import_jsx_runtime9.jsx)("stop", { offset: ".071", stopOpacity: ".2" }),
            (0, import_jsx_runtime9.jsx)("stop", { offset: ".321", stopOpacity: ".1" }),
            (0, import_jsx_runtime9.jsx)("stop", { offset: ".623", stopOpacity: ".05" }),
            (0, import_jsx_runtime9.jsx)("stop", { offset: "1", stopOpacity: "0" })
          ]
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "url(#k8yl7~hDat~FaoWq8WjN6b)",
          d: "M17.634,6c-0.783-0.003-1.476,0.504-1.712,1.25L5.005,39.595 c-0.335,0.934,0.151,1.964,1.085,2.299C6.286,41.964,6.493,42,6.702,42h9.026c0.684-0.122,1.25-0.603,1.481-1.259l2.177-6.416 l7.776,7.253c0.326,0.27,0.735,0.419,1.158,0.422h10.114l-4.436-12.676l-12.931,0.003L28.98,6H17.634z"
        }
      ),
      (0, import_jsx_runtime9.jsxs)(
        "linearGradient",
        {
          id: "k8yl7~hDat~FaoWq8WjN6c",
          x1: "-1252.952",
          x2: "-1244.704",
          y1: "876.6",
          y2: "898.575",
          gradientTransform: "translate(1981.75 -1362.063) scale(1.5625)",
          gradientUnits: "userSpaceOnUse",
          children: [
            (0, import_jsx_runtime9.jsx)("stop", { offset: "0", stopColor: "#3ccbf4" }),
            (0, import_jsx_runtime9.jsx)("stop", { offset: "1", stopColor: "#2892df" })
          ]
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "url(#k8yl7~hDat~FaoWq8WjN6c)",
          d: "M32.074,7.225C31.827,6.493,31.141,6,30.368,6h-12.6c0.772,0,1.459,0.493,1.705,1.224 l10.935,32.399c0.318,0.942-0.188,1.963-1.13,2.281C29.093,41.968,28.899,42,28.703,42h12.6c0.994,0,1.8-0.806,1.8-1.801 c0-0.196-0.032-0.39-0.095-0.575L32.074,7.225z"
        }
      )
    ]
  }
);
var KeycloakIcon = (props) => (0, import_jsx_runtime9.jsx)(
  SvgIcon_default,
  {
    ...props,
    width: "512",
    height: "512",
    viewBox: "0 0 512 512",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    children: (0, import_jsx_runtime9.jsx)(
      "path",
      {
        d: "M472.136 163.959H408.584C407.401 163.959 406.218 163.327 405.666 162.3L354.651 73.6591C354.02 72.632 352.916 72 351.654 72H143.492C142.309 72 141.126 72.632 140.574 73.6591L87.5084 165.618L36.414 254.259C35.862 255.286 35.862 256.55 36.414 257.656L87.5084 346.297L140.495 438.335C141.047 439.362 142.23 440.073 143.413 439.994H351.654C352.837 439.994 354.02 439.362 354.651 438.335L405.745 349.694C406.297 348.667 407.48 347.956 408.663 348.035H472.215C474.344 348.035 476 346.297 476 344.243V167.83C475.921 165.697 474.186 163.959 472.136 163.959ZM228.728 349.694L212.721 377.345C212.485 377.74 212.091 378.135 211.696 378.372C211.223 378.609 210.75 378.767 210.198 378.767H178.422C177.318 378.767 176.293 378.214 175.82 377.187L128.431 294.787L123.779 286.65L106.748 257.498C106.511 257.103 106.353 256.629 106.432 256.076C106.432 255.602 106.59 255.049 106.827 254.654L123.937 224.949L175.899 134.886C176.451 133.938 177.476 133.306 178.501 133.306H210.198C210.75 133.306 211.302 133.464 211.854 133.701C212.248 133.938 212.643 134.254 212.879 134.728L228.886 162.537C229.359 163.485 229.28 164.67 228.728 165.539L177.397 254.654C177.16 255.049 177.081 255.523 177.081 255.918C177.081 256.392 177.239 256.787 177.397 257.182L228.728 346.218C229.438 347.403 229.359 348.667 228.728 349.694V349.694ZM388.083 257.498L371.051 286.65L366.399 294.787L319.011 377.187C318.459 378.135 317.512 378.767 316.409 378.767H284.632C284.08 378.767 283.607 378.609 283.134 378.372C282.74 378.135 282.346 377.819 282.109 377.345L266.103 349.694C265.393 348.667 265.393 347.403 266.024 346.376L317.355 257.34C317.591 256.945 317.67 256.471 317.67 256.076C317.67 255.602 317.513 255.207 317.355 254.812L266.024 165.697C265.472 164.749 265.393 163.643 265.866 162.695L281.873 134.886C282.109 134.491 282.503 134.096 282.898 133.859C283.371 133.543 283.923 133.464 284.553 133.464H316.409C317.512 133.464 318.538 134.017 319.011 135.044L370.972 225.107L388.083 254.812C388.319 255.286 388.477 255.76 388.477 256.234C388.477 256.55 388.319 257.024 388.083 257.498V257.498Z",
        fill: "#008AAA"
      }
    )
  }
);
var LinkedinIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: [
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#0288D1",
          d: "M42,37c0,2.762-2.238,5-5,5H11c-2.761,0-5-2.238-5-5V11c0-2.762,2.239-5,5-5h26c2.762,0,5,2.238,5,5V37z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#FFF",
          d: "M12 19H17V36H12zM14.485 17h-.028C12.965 17 12 15.888 12 14.499 12 13.08 12.995 12 14.514 12c1.521 0 2.458 1.08 2.486 2.499C17 15.887 16.035 17 14.485 17zM36 36h-5v-9.099c0-2.198-1.225-3.698-3.192-3.698-1.501 0-2.313 1.012-2.707 1.99C24.957 25.543 25 26.511 25 27v9h-5V19h5v2.616C25.721 20.5 26.85 19 29.738 19c3.578 0 6.261 2.25 6.261 7.274L36 36 36 36z"
        }
      )
    ]
  }
);
var NotionIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    fillRule: "evenodd",
    clipRule: "evenodd",
    children: [
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#fff",
          fillRule: "evenodd",
          d: "M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z",
          clipRule: "evenodd"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#fff",
          fillRule: "evenodd",
          d: "M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619 l23.971-1.387c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463 C13.171,14.718,12.862,15.181,12.862,16.182L12.862,16.182z",
          clipRule: "evenodd"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#424242",
          fillRule: "evenodd",
          d: "M11.553,11.099c1.232,1.001,1.694,0.925,4.008,0.77 l21.812-1.31c0.463,0,0.078-0.461-0.076-0.538l-3.622-2.619c-0.694-0.539-1.619-1.156-3.391-1.002l-21.12,1.54 c-0.77,0.076-0.924,0.461-0.617,0.77L11.553,11.099z M12.862,16.182v22.95c0,1.233,0.616,1.695,2.004,1.619l23.971-1.387 c1.388-0.076,1.543-0.925,1.543-1.927V14.641c0-1-0.385-1.54-1.234-1.463l-25.05,1.463C13.171,14.718,12.862,15.181,12.862,16.182 L12.862,16.182z M36.526,17.413c0.154,0.694,0,1.387-0.695,1.465l-1.155,0.23v16.943c-1.003,0.539-1.928,0.847-2.698,0.847 c-1.234,0-1.543-0.385-2.467-1.54l-7.555-11.86v11.475l2.391,0.539c0,0,0,1.386-1.929,1.386l-5.317,0.308 c-0.154-0.308,0-1.078,0.539-1.232l1.388-0.385V20.418l-1.927-0.154c-0.155-0.694,0.23-1.694,1.31-1.772l5.704-0.385l7.862,12.015 V19.493l-2.005-0.23c-0.154-0.848,0.462-1.464,1.233-1.54L36.526,17.413z M7.389,5.862l21.968-1.618 c2.698-0.231,3.392-0.076,5.087,1.155l7.013,4.929C42.614,11.176,43,11.407,43,12.33v27.032c0,1.694-0.617,2.696-2.775,2.849 l-25.512,1.541c-1.62,0.077-2.391-0.154-3.239-1.232l-5.164-6.7C5.385,34.587,5,33.664,5,32.585V8.556 C5,7.171,5.617,6.015,7.389,5.862z",
          clipRule: "evenodd"
        }
      )
    ]
  }
);
var SlackIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 48 48",
    width: "512px",
    height: "512px",
    children: [
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#33d375",
          d: "M33,8c0-2.209-1.791-4-4-4s-4,1.791-4,4c0,1.254,0,9.741,0,11c0,2.209,1.791,4,4,4s4-1.791,4-4	C33,17.741,33,9.254,33,8z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#33d375",
          d: "M43,19c0,2.209-1.791,4-4,4c-1.195,0-4,0-4,0s0-2.986,0-4c0-2.209,1.791-4,4-4S43,16.791,43,19z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#40c4ff",
          d: "M8,14c-2.209,0-4,1.791-4,4s1.791,4,4,4c1.254,0,9.741,0,11,0c2.209,0,4-1.791,4-4s-1.791-4-4-4	C17.741,14,9.254,14,8,14z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#40c4ff",
          d: "M19,4c2.209,0,4,1.791,4,4c0,1.195,0,4,0,4s-2.986,0-4,0c-2.209,0-4-1.791-4-4S16.791,4,19,4z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#e91e63",
          d: "M14,39.006C14,41.212,15.791,43,18,43s4-1.788,4-3.994c0-1.252,0-9.727,0-10.984	c0-2.206-1.791-3.994-4-3.994s-4,1.788-4,3.994C14,29.279,14,37.754,14,39.006z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#e91e63",
          d: "M4,28.022c0-2.206,1.791-3.994,4-3.994c1.195,0,4,0,4,0s0,2.981,0,3.994c0,2.206-1.791,3.994-4,3.994	S4,30.228,4,28.022z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#ffc107",
          d: "M39,33c2.209,0,4-1.791,4-4s-1.791-4-4-4c-1.254,0-9.741,0-11,0c-2.209,0-4,1.791-4,4s1.791,4,4,4	C29.258,33,37.746,33,39,33z"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          fill: "#ffc107",
          d: "M28,43c-2.209,0-4-1.791-4-4c0-1.195,0-4,0-4s2.986,0,4,0c2.209,0,4,1.791,4,4S30.209,43,28,43z"
        }
      )
    ]
  }
);
var SpotifyIcon = (props) => (0, import_jsx_runtime9.jsx)(
  SvgIcon_default,
  {
    ...props,
    width: "512",
    height: "512",
    viewBox: "0 0 512 512",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    children: (0, import_jsx_runtime9.jsx)(
      "path",
      {
        d: "M255.498 31.0034C131.513 31.0034 31 131.515 31 255.502C31 379.492 131.513 480 255.498 480C379.497 480 480 379.495 480 255.502C480 131.522 379.497 31.0135 255.495 31.0135L255.498 31V31.0034ZM358.453 354.798C354.432 361.391 345.801 363.486 339.204 359.435C286.496 327.237 220.139 319.947 141.993 337.801C134.463 339.516 126.957 334.798 125.24 327.264C123.516 319.731 128.217 312.225 135.767 310.511C221.284 290.972 294.639 299.384 353.816 335.549C360.413 339.596 362.504 348.2 358.453 354.798ZM385.932 293.67C380.864 301.903 370.088 304.503 361.858 299.438C301.512 262.345 209.528 251.602 138.151 273.272C128.893 276.067 119.118 270.851 116.309 261.61C113.521 252.353 118.74 242.597 127.981 239.782C209.512 215.044 310.87 227.026 380.17 269.612C388.4 274.68 391 285.456 385.935 293.676V293.673L385.932 293.67ZM388.293 230.016C315.935 187.039 196.56 183.089 127.479 204.055C116.387 207.42 104.654 201.159 101.293 190.063C97.9326 178.964 104.189 167.241 115.289 163.87C194.59 139.796 326.418 144.446 409.723 193.902C419.722 199.826 422.995 212.71 417.068 222.675C411.168 232.653 398.247 235.943 388.303 230.016H388.293V230.016Z",
        fill: "#1ED760"
      }
    )
  }
);
var TwitchIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    width: "512",
    height: "512",
    viewBox: "0 0 512 512",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    children: [
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          d: "M416 240L352 304H288L232 360V304H160V64H416V240Z",
          fill: "white"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          d: "M144 32L64 112V400H160V480L240 400H304L448 256V32H144ZM416 240L352 304H288L232 360V304H160V64H416V240Z",
          fill: "#9146FF"
        }
      ),
      (0, import_jsx_runtime9.jsx)("path", { d: "M368 120H336V216H368V120Z", fill: "#9146FF" }),
      (0, import_jsx_runtime9.jsx)("path", { d: "M280 120H248V216H280V120Z", fill: "#9146FF" })
    ]
  }
);
var WorkosIcon = (props) => (0, import_jsx_runtime9.jsxs)(
  SvgIcon_default,
  {
    ...props,
    width: "512",
    height: "512",
    viewBox: "0 0 512 512",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    children: [
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          d: "M33 256.043C33 264.556 35.3159 273.069 39.4845 280.202L117.993 415.493C126.098 429.298 138.373 440.572 153.657 445.634C183.764 455.528 214.797 442.873 229.618 417.333L248.609 384.661L173.806 256.043L252.777 119.831L271.768 87.1591C277.557 77.2654 284.968 69.4424 294 63H285.894H172.185C150.878 63 131.193 74.2742 120.54 92.6812L39.7161 231.884C35.3159 239.016 33 247.53 33 256.043Z",
          fill: "#6363F1"
        }
      ),
      (0, import_jsx_runtime9.jsx)(
        "path",
        {
          d: "M480 256.058C480 247.539 477.684 239.021 473.516 231.883L393.849 94.6596C379.028 69.3331 347.995 56.4396 317.888 66.34C302.603 71.4053 290.329 82.6871 282.224 96.5015L264.391 127.354L339.194 256.058L260.223 392.131L241.232 424.825C235.443 434.495 228.032 442.553 219 449H227.106H340.815C362.122 449 381.807 437.718 392.46 419.299L473.284 280.003C477.684 272.866 480 264.577 480 256.058Z",
          fill: "#6363F1"
        }
      )
    ]
  }
);

// node_modules/ra-supabase-ui-materialui/src/SocialAuthButton.tsx
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var SocialAuthButton = ({
  provider,
  redirect: redirectTo,
  ...props
}) => {
  const login = useLogin_default();
  const notify = useNotify();
  const handleClick = () => {
    login({ provider }, redirectTo ?? window.location.toString()).catch(
      (error) => {
        if (error) {
          notify(error.message, { type: "error" });
        }
      }
    );
  };
  return (0, import_jsx_runtime10.jsx)(
    Button_default,
    {
      onClick: handleClick,
      variant: "contained",
      size: "medium",
      color: "inherit",
      ...props
    }
  );
};
var AppleButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Apple"
  });
  return (0, import_jsx_runtime10.jsx)(SocialAuthButton, { startIcon: (0, import_jsx_runtime10.jsx)(AppleIcon, {}), provider: "apple", ...props, children: label });
};
var AzureButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Azure"
  });
  return (0, import_jsx_runtime10.jsx)(SocialAuthButton, { startIcon: (0, import_jsx_runtime10.jsx)(AzureIcon, {}), provider: "azure", ...props, children: label });
};
var BitbucketButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Bitbucket"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(BitbucketIcon, {}),
      provider: "bitbucket",
      ...props,
      children: label
    }
  );
};
var DiscordButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Discord"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(DiscordIcon, {}),
      provider: "discord",
      ...props,
      children: label
    }
  );
};
var FacebookButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Facebook"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(FacebookIcon, {}),
      provider: "facebook",
      ...props,
      children: label
    }
  );
};
var GitlabButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Gitlab"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(GitlabIcon, {}),
      provider: "gitlab",
      ...props,
      children: label
    }
  );
};
var GithubButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Github"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(GithubIcon, {}),
      provider: "github",
      ...props,
      children: label
    }
  );
};
var GoogleButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Google"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(GoogleIcon, {}),
      provider: "google",
      ...props,
      children: label
    }
  );
};
var KeycloakButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Keycloak"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(KeycloakIcon, {}),
      provider: "keycloak",
      ...props,
      children: label
    }
  );
};
var LinkedInButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "LinkedIn"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(LinkedinIcon, {}),
      provider: "linkedin",
      ...props,
      children: label
    }
  );
};
var NotionButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Notion"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(NotionIcon, {}),
      provider: "notion",
      ...props,
      children: label
    }
  );
};
var SlackButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Slack"
  });
  return (0, import_jsx_runtime10.jsx)(SocialAuthButton, { startIcon: (0, import_jsx_runtime10.jsx)(SlackIcon, {}), provider: "slack", ...props, children: label });
};
var SpotifyButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Spotify"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(SpotifyIcon, {}),
      provider: "spotify",
      ...props,
      children: label
    }
  );
};
var TwitchButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Twitch"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(TwitchIcon, {}),
      provider: "twitch",
      ...props,
      children: label
    }
  );
};
var TwitterButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "Twitter"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(TwitterIcon, {}),
      provider: "twitter",
      ...props,
      children: label
    }
  );
};
var WorkosButton = (props) => {
  const translate = useTranslate();
  const label = translate("ra-supabase.auth.sign_in_with", {
    provider: "WorkOS"
  });
  return (0, import_jsx_runtime10.jsx)(
    SocialAuthButton,
    {
      startIcon: (0, import_jsx_runtime10.jsx)(WorkosIcon, {}),
      provider: "workos",
      ...props,
      children: label
    }
  );
};

// node_modules/ra-supabase-ui-materialui/src/LoginPage.tsx
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var LoginPage = (props) => {
  const {
    children,
    disableEmailPassword = false,
    disableForgotPassword = false,
    providers = []
  } = props;
  return (0, import_jsx_runtime11.jsx)(AuthLayout, { children: children ?? (0, import_jsx_runtime11.jsxs)(import_jsx_runtime11.Fragment, { children: [
    disableEmailPassword ? null : (0, import_jsx_runtime11.jsx)(
      LoginForm,
      {
        disableForgotPassword
      }
    ),
    disableEmailPassword || providers.length === 0 ? null : (0, import_jsx_runtime11.jsx)(Divider_default, {}),
    providers && providers.length > 0 ? (0, import_jsx_runtime11.jsx)(import_jsx_runtime11.Fragment, { children: (0, import_jsx_runtime11.jsxs)(Stack_default, { gap: 1, padding: 1, children: [
      providers.includes("apple") ? (0, import_jsx_runtime11.jsx)(AppleButton, {}) : null,
      providers.includes("azure") ? (0, import_jsx_runtime11.jsx)(AzureButton, {}) : null,
      providers.includes("bitbucket") ? (0, import_jsx_runtime11.jsx)(BitbucketButton, {}) : null,
      providers.includes("discord") ? (0, import_jsx_runtime11.jsx)(DiscordButton, {}) : null,
      providers.includes("facebook") ? (0, import_jsx_runtime11.jsx)(FacebookButton, {}) : null,
      providers.includes("gitlab") ? (0, import_jsx_runtime11.jsx)(GitlabButton, {}) : null,
      providers.includes("github") ? (0, import_jsx_runtime11.jsx)(GithubButton, {}) : null,
      providers.includes("google") ? (0, import_jsx_runtime11.jsx)(GoogleButton, {}) : null,
      providers.includes("keycloak") ? (0, import_jsx_runtime11.jsx)(KeycloakButton, {}) : null,
      providers.includes("linkedin") ? (0, import_jsx_runtime11.jsx)(LinkedInButton, {}) : null,
      providers.includes("notion") ? (0, import_jsx_runtime11.jsx)(NotionButton, {}) : null,
      providers.includes("slack") ? (0, import_jsx_runtime11.jsx)(SlackButton, {}) : null,
      providers.includes("spotify") ? (0, import_jsx_runtime11.jsx)(SpotifyButton, {}) : null,
      providers.includes("twitch") ? (0, import_jsx_runtime11.jsx)(TwitchButton, {}) : null,
      providers.includes("twitter") ? (0, import_jsx_runtime11.jsx)(TwitterButton, {}) : null,
      providers.includes("workos") ? (0, import_jsx_runtime11.jsx)(WorkosButton, {}) : null
    ] }) }) : null
  ] }) });
};

// node_modules/ra-supabase-ui-materialui/src/SetPasswordForm.tsx
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var SetPasswordForm = () => {
  const access_token = useSupabaseAccessToken();
  const refresh_token = useSupabaseAccessToken({
    parameterName: "refresh_token"
  });
  const notify = useNotify();
  const translate = useTranslate();
  const [setPassword] = useSetPassword({
    onError: (error) => {
      notify(
        typeof error === "string" ? error : typeof error === "undefined" || !error.message ? "ra.auth.sign_in_error" : error.message,
        {
          type: "warning",
          messageArgs: {
            _: typeof error === "string" ? error : error && error.message ? error.message : void 0
          }
        }
      );
    }
  });
  const validate = (values) => {
    if (values.password !== values.confirmPassword) {
      return {
        password: "ra-supabase.validation.password_mismatch",
        confirmPassword: "ra-supabase.validation.password_mismatch"
      };
    }
    return {};
  };
  if (!access_token || !refresh_token) {
    if (true) {
      console.error(
        "Missing access_token or refresh_token for set password"
      );
    }
    return (0, import_jsx_runtime12.jsx)("div", { className: SupabaseLoginFormClasses3.container, children: (0, import_jsx_runtime12.jsx)("div", { children: translate("ra-supabase.auth.missing_tokens") }) });
  }
  const submit = (values) => {
    return setPassword({
      access_token,
      refresh_token,
      password: values.password
    });
  };
  return (0, import_jsx_runtime12.jsxs)(Root4, { onSubmit: submit, validate, children: [
    (0, import_jsx_runtime12.jsxs)("div", { className: SupabaseLoginFormClasses3.container, children: [
      (0, import_jsx_runtime12.jsx)(Typography_default, { variant: "h5", textAlign: "center", gutterBottom: true, children: translate("ra-supabase.set_password.new_password", {
        _: "Choose your password"
      }) }),
      (0, import_jsx_runtime12.jsx)("div", { className: SupabaseLoginFormClasses3.input, children: (0, import_jsx_runtime12.jsx)(
        PasswordInput,
        {
          source: "password",
          label: translate("ra.auth.password", {
            _: "Password"
          }),
          autoComplete: "new-password",
          fullWidth: true,
          validate: required()
        }
      ) }),
      (0, import_jsx_runtime12.jsx)("div", { children: (0, import_jsx_runtime12.jsx)(
        PasswordInput,
        {
          source: "confirmPassword",
          label: translate("ra.auth.confirm_password", {
            _: "Confirm password"
          }),
          fullWidth: true,
          validate: required()
        }
      ) })
    ] }),
    (0, import_jsx_runtime12.jsx)(CardActions_default, { children: (0, import_jsx_runtime12.jsx)(
      SaveButton,
      {
        variant: "contained",
        type: "submit",
        className: SupabaseLoginFormClasses3.button,
        label: translate("ra.action.save")
      }
    ) })
  ] });
};
var PREFIX4 = "RaSupabaseSetPasswordForm";
var SupabaseLoginFormClasses3 = {
  container: `${PREFIX4}-container`,
  input: `${PREFIX4}-input`,
  button: `${PREFIX4}-button`
};
var Root4 = styled_default(Form, {
  name: PREFIX4,
  overridesResolver: (props, styles) => styles.root
})(({ theme }) => ({
  [`& .${SupabaseLoginFormClasses3.container}`]: {
    padding: "0 1em 0 1em"
  },
  [`& .${SupabaseLoginFormClasses3.input}`]: {
    marginTop: "1em"
  },
  [`& .${SupabaseLoginFormClasses3.button}`]: {
    width: "100%"
  }
}));

// node_modules/ra-supabase-ui-materialui/src/SetPasswordPage.tsx
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var SetPasswordPage = (props) => {
  const { children = (0, import_jsx_runtime13.jsx)(SetPasswordForm, {}) } = props;
  return (0, import_jsx_runtime13.jsx)(AuthLayout, { children });
};
SetPasswordPage.path = "/set-password";

// node_modules/ra-supabase-language-english/src/index.ts
var raSupabaseEnglishMessages = {
  "ra-supabase": {
    auth: {
      email: "Email",
      confirm_password: "Confirm password",
      sign_in_with: "Sign in with %{provider}",
      forgot_password: "Forgot password?",
      reset_password: "Reset password",
      password_reset: "Your password has been reset. You will receive an email containing a link to log in.",
      missing_tokens: "Access and refresh tokens are missing",
      back_to_login: "Back to login"
    },
    reset_password: {
      forgot_password: "Forgot password?",
      forgot_password_details: "Enter your email for instructions."
    },
    set_password: {
      new_password: "Choose your password"
    },
    validation: {
      password_mismatch: "Passwords do not match"
    }
  }
};

// node_modules/ra-supabase/src/defaultI18nProvider.ts
var defaultI18nProvider = src_default(() => {
  return mergeTranslations(src_default2, raSupabaseEnglishMessages);
}, "en");

// node_modules/ra-supabase/src/AdminGuesser.tsx
var React6 = __toESM(require_react());
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var AdminGuesser = (props) => {
  const {
    instanceUrl,
    apiKey,
    dataProvider,
    authProvider,
    basename,
    darkTheme,
    defaultTheme: defaultTheme2,
    i18nProvider = defaultI18nProvider,
    lightTheme,
    queryClient,
    store,
    theme,
    ...rest
  } = props;
  const defaultSupabaseClient = instanceUrl && apiKey ? createClient(instanceUrl, apiKey) : null;
  const defaultDataProvider = instanceUrl && apiKey && defaultSupabaseClient ? supabaseDataProvider({
    instanceUrl,
    apiKey,
    supabaseClient: defaultSupabaseClient
  }) : void 0;
  const defaultAuthProvider = instanceUrl && apiKey && defaultSupabaseClient ? supabaseAuthProvider(defaultSupabaseClient, {}) : void 0;
  return (0, import_jsx_runtime14.jsx)(BrowserRouter, { children: (0, import_jsx_runtime14.jsx)(
    AdminContext,
    {
      authProvider: authProvider ?? defaultAuthProvider,
      basename,
      darkTheme,
      dataProvider: dataProvider ?? defaultDataProvider,
      defaultTheme: defaultTheme2,
      i18nProvider,
      lightTheme,
      queryClient,
      store,
      theme,
      children: (0, import_jsx_runtime14.jsx)(AdminUIGuesser, { ...rest })
    }
  ) });
};
var AdminUIGuesser = (props) => {
  const resourceDefinitions = useCrudGuesser();
  const { children, ...rest } = props;
  const [CatchAll, setCatchAll] = React6.useState(() => Loading);
  React6.useEffect(() => {
    if (!children && resourceDefinitions.length > 0) {
      console.log(
        `Guessed Admin:

import { Admin, Resource, CustomRoutes } from 'react-admin';
import { BrowserRouter, Route } from 'react-router-dom';
import { createClient } from '@supabase/supabase-js';
import {
    CreateGuesser,
    EditGuesser,
    ForgotPasswordPage,
    ListGuesser,
    LoginPage,
    SetPasswordPage,
    ShowGuesser,
    defaultI18nProvider,
    supabaseDataProvider,
    supabaseAuthProvider
} from 'ra-supabase';   

const instanceUrl = YOUR_SUPABASE_URL;
const apiKey = YOUR_SUPABASE_API_KEY;
const supabaseClient = createClient(instanceUrl, apiKey);
const dataProvider = supabaseDataProvider({ instanceUrl, apiKey, supabaseClient });
const authProvider = supabaseAuthProvider(supabaseClient, {});

export const App = () => (
    <BrowserRouter>
        <Admin
            dataProvider={dataProvider}
            authProvider={authProvider}
            i18nProvider={defaultI18nProvider}
            loginPage={LoginPage}
        >${resourceDefinitions.map(
          (def) => `
            <Resource name="${def.name}"${def.list ? " list={ListGuesser}" : ""}${def.edit ? " edit={EditGuesser}" : ""}${def.create ? " create={CreateGuesser}" : ""}${def.show ? " show={ShowGuesser}" : ""} />`
        ).join("")}
            <CustomRoutes noLayout>
                <Route path={SetPasswordPage.path} element={<SetPasswordPage />} />
                <Route path={ForgotPasswordPage.path} element={<ForgotPasswordPage />} />
            </CustomRoutes>
        </Admin>
    </BrowserRouter>
);`
      );
    }
  }, [resourceDefinitions, children]);
  React6.useEffect(() => {
    if (!children && resourceDefinitions.length > 0) {
      setCatchAll(void 0);
    }
  }, [resourceDefinitions, children]);
  const resourceElements = resourceDefinitions.map((resourceDefinition) => (0, import_jsx_runtime14.jsx)(Resource, { ...resourceDefinition }, resourceDefinition.name));
  return (0, import_jsx_runtime14.jsxs)(
    AdminUI,
    {
      ready: Loading,
      catchAll: CatchAll,
      loginPage: LoginPage,
      ...rest,
      children: [
        children ?? resourceElements,
        (0, import_jsx_runtime14.jsxs)(CustomRoutes, { noLayout: true, children: [
          (0, import_jsx_runtime14.jsx)(
            Route,
            {
              path: SetPasswordPage.path,
              element: (0, import_jsx_runtime14.jsx)(SetPasswordPage, {})
            }
          ),
          (0, import_jsx_runtime14.jsx)(
            Route,
            {
              path: ForgotPasswordPage.path,
              element: (0, import_jsx_runtime14.jsx)(ForgotPasswordPage, {})
            }
          )
        ] })
      ]
    }
  );
};
export {
  AdminGuesser,
  AppleButton,
  AppleIcon,
  AuthLayout,
  AuthLayoutClasses,
  AzureButton,
  AzureIcon,
  BitbucketButton,
  BitbucketIcon,
  CreateGuesser,
  CreateGuesserView,
  DiscordButton,
  DiscordIcon,
  EditGuesser,
  EditGuesserView,
  FacebookButton,
  FacebookIcon,
  ForgotPasswordForm,
  ForgotPasswordPage,
  GithubButton,
  GithubIcon,
  GitlabButton,
  GitlabIcon,
  GoogleButton,
  GoogleIcon,
  KeycloakButton,
  KeycloakIcon,
  LinkedInButton,
  LinkedinIcon,
  ListGuesser,
  ListGuesserView,
  LoginForm,
  LoginPage,
  NotionButton,
  NotionIcon,
  SetPasswordForm,
  SetPasswordPage,
  ShowGuesser,
  ShowGuesserView,
  SlackButton,
  SlackIcon,
  SocialAuthButton,
  SpotifyButton,
  SpotifyIcon,
  TwitchButton,
  TwitchIcon,
  TwitterButton,
  TwitterIcon,
  WorkosButton,
  WorkosIcon,
  defaultI18nProvider,
  raSupabaseEnglishMessages,
  supabaseAuthProvider,
  supabaseDataProvider,
  supabaseHttpClient,
  useAPISchema,
  useCrudGuesser,
  useRedirectIfAuthenticated,
  useResetPassword,
  useSetPassword,
  useSupabaseAccessToken
};
//# sourceMappingURL=ra-supabase.js.map
