{"version": 3, "sources": ["../../@mui/material/utils/createChainedFunction.js", "../../@mui/material/utils/debounce.js", "../../@mui/material/utils/deprecatedPropType.js", "../../@mui/material/utils/isMuiElement.js", "../../@mui/material/utils/ownerDocument.js", "../../@mui/material/utils/ownerWindow.js", "../../@mui/material/utils/requirePropFactory.js", "../../@mui/material/utils/setRef.js", "../../@mui/material/utils/useEnhancedEffect.js", "../../@mui/material/utils/useId.js", "../../@mui/material/utils/unsupportedProp.js", "../../@mui/material/utils/useControlled.js", "../../@mui/material/utils/index.js"], "sourcesContent": ["import createChainedFunction from '@mui/utils/createChainedFunction';\nexport default createChainedFunction;", "import debounce from '@mui/utils/debounce';\nexport default debounce;", "import deprecatedPropType from '@mui/utils/deprecatedPropType';\nexport default deprecatedPropType;", "import isMuiElement from '@mui/utils/isMuiElement';\nexport default isMuiElement;", "import ownerDocument from '@mui/utils/ownerDocument';\nexport default ownerDocument;", "import ownerWindow from '@mui/utils/ownerWindow';\nexport default ownerWindow;", "import requirePropFactory from '@mui/utils/requirePropFactory';\nexport default requirePropFactory;", "import setRef from '@mui/utils/setRef';\nexport default setRef;", "'use client';\n\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport default useEnhancedEffect;", "'use client';\n\nimport useId from '@mui/utils/useId';\nexport default useId;", "import unsupportedProp from '@mui/utils/unsupportedProp';\nexport default unsupportedProp;", "'use client';\n\nimport useControlled from '@mui/utils/useControlled';\nexport default useControlled;", "'use client';\n\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '@mui/utils';\nexport { default as capitalize } from './capitalize';\nexport { default as createChainedFunction } from './createChainedFunction';\nexport { default as createSvgIcon } from './createSvgIcon';\nexport { default as debounce } from './debounce';\nexport { default as deprecatedPropType } from './deprecatedPropType';\nexport { default as isMuiElement } from './isMuiElement';\nexport { default as ownerDocument } from './ownerDocument';\nexport { default as ownerWindow } from './ownerWindow';\nexport { default as requirePropFactory } from './requirePropFactory';\nexport { default as setRef } from './setRef';\nexport { default as unstable_useEnhancedEffect } from './useEnhancedEffect';\nexport { default as unstable_useId } from './useId';\nexport { default as unsupportedProp } from './unsupportedProp';\nexport { default as useControlled } from './useControlled';\nexport { default as useEventCallback } from './useEventCallback';\nexport { default as useForkRef } from './useForkRef';\nexport { default as useIsFocusVisible } from './useIsFocusVisible';\n// TODO: remove this export once ClassNameGenerator is stable\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_ClassNameGenerator = {\n  configure: generator => {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(['MUI: `ClassNameGenerator` import from `@mui/material/utils` is outdated and might cause unexpected issues.', '', \"You should use `import { unstable_ClassNameGenerator } from '@mui/material/className'` instead\", '', 'The detail of the issue: https://github.com/mui/material-ui/issues/30011#issuecomment-1024993401', '', 'The updated documentation: https://mui.com/guides/classname-generator/'].join('\\n'));\n    }\n    ClassNameGenerator.configure(generator);\n  }\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IACO;AADP,IAAAA,8BAAA;AAAA;AAAA;AACA,IAAO,gCAAQ;AAAA;AAAA;;;ACDf,IACO;AADP,IAAAC,iBAAA;AAAA;AAAA;AACA,IAAO,mBAAQ;AAAA;AAAA;;;ACDf,IACO;AADP,IAAAC,2BAAA;AAAA;AAAA;AACA,IAAO,6BAAQ;AAAA;AAAA;;;ACDf,IACO;AADP,IAAAC,qBAAA;AAAA;AAAA;AACA,IAAO,uBAAQ;AAAA;AAAA;;;ACDf,IACO;AADP,IAAAC,sBAAA;AAAA;AAAA;AACA,IAAO,wBAAQ;AAAA;AAAA;;;ACDf,IACO;AADP,IAAAC,oBAAA;AAAA;AAAA;AACA,IAAO,sBAAQ;AAAA;AAAA;;;ACDf,IACO;AADP,IAAAC,2BAAA;AAAA;AAAA;AACA,IAAO,6BAAQ;AAAA;AAAA;;;ACDf,IACO;AADP,IAAAC,eAAA;AAAA;AAAA;AACA,IAAO,iBAAQ;AAAA;AAAA;;;ACDf,IAGOC;AAHP,IAAAC,0BAAA;AAAA;AAAA;AAEA;AACA,IAAOD,6BAAQ;AAAA;AAAA;;;ACHf,IAGO;AAHP,IAAAE,cAAA;AAAA;AAAA;AAEA;AACA,IAAO,gBAAQ;AAAA;AAAA;;;ACHf,IACO;AADP,IAAAC,wBAAA;AAAA;AAAA;AACA,IAAO,0BAAQ;AAAA;AAAA;;;ACDf,IAGO;AAHP,IAAAC,sBAAA;AAAA;AAAA;AAEA;AACA,IAAO,wBAAQ;AAAA;AAAA;;;ACHf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oCAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAsBa;AAtBb;AAAA;AAAA;AAEA;AACA;AACA,IAAAC;AACA;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA,IAAAC;AACA;AACA;AACA;AAGO,IAAM,8BAA8B;AAAA,MACzC,WAAW,eAAa;AACtB,YAAI,MAAuC;AACzC,kBAAQ,KAAK,CAAC,8GAA8G,IAAI,kGAAkG,IAAI,oGAAoG,IAAI,wEAAwE,EAAE,KAAK,IAAI,CAAC;AAAA,QACpa;AACA,mCAAmB,UAAU,SAAS;AAAA,MACxC;AAAA,IACF;AAAA;AAAA;", "names": ["init_createChainedFunction", "init_debounce", "init_deprecatedPropType", "init_isMuiElement", "init_ownerDocument", "init_ownerWindow", "init_requirePropFactory", "init_setRef", "useEnhancedEffect_default", "init_useEnhancedEffect", "init_useId", "init_unsupportedProp", "init_useControlled", "useEnhancedEffect_default", "init_createChainedFunction", "init_debounce", "init_deprecatedPropType", "init_isMuiElement", "init_ownerDocument", "init_ownerWindow", "init_requirePropFactory", "init_setRef", "init_useEnhancedEffect", "init_useId", "init_unsupportedProp", "init_useControlled"]}